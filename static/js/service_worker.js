const CACHE_NAME = 'librainian-v2.45';
const urlsToCache = [
  '/',
  '/static/css/style.css',
  '/static/css/dark-mode.css',
  '/static/js/main.js',
  '/static/img/librainian-logo-black-transparent-med.png',
  // Attendance system resources
  '/attendance/scanner/',
  '/attendance/today/',
  '/attendance/log/',
  // External QR scanner library (will be cached when accessed)
  'https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => response || fetch(event.request))
  );
});

// ... existing service worker code ...

self.addEventListener('push', function(event) {
    const options = {
      body: event.data.text(),
      icon: '/static/img/librainian-logo-black-transparent.png',
      badge: '/static/img/librainian-logo-black-transparent.png'
    };
  
    event.waitUntil(
      self.registration.showNotification('Librainian Notification', options)
    );
  });
  
  self.addEventListener('notificationclick', function(event) {
    event.notification.close();
    event.waitUntil(
      clients.openWindow('https://librainian.com')
    );
  });