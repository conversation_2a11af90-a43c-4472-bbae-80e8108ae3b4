from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from attendance.models import AttendanceRecord, AttendanceSettings, AttendanceStatistics


class Command(BaseCommand):
    help = 'Set up attendance system groups and permissions'

    def handle(self, *args, **options):
        # Create groups
        admin_group, created = Group.objects.get_or_create(name='admin')
        if created:
            self.stdout.write(self.style.SUCCESS('Created admin group'))
        
        staff_group, created = Group.objects.get_or_create(name='staff')
        if created:
            self.stdout.write(self.style.SUCCESS('Created staff group'))
        
        scanner_only_group, created = Group.objects.get_or_create(name='scanner_only')
        if created:
            self.stdout.write(self.style.SUCCESS('Created scanner_only group'))

        # Get content types
        attendance_ct = ContentType.objects.get_for_model(AttendanceRecord)
        settings_ct = ContentType.objects.get_for_model(AttendanceSettings)
        stats_ct = ContentType.objects.get_for_model(AttendanceStatistics)

        # Define permissions for each group
        
        # Admin permissions (full access)
        admin_permissions = [
            # AttendanceRecord permissions
            Permission.objects.get_or_create(
                codename='can_view_attendance',
                name='Can view attendance records',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_add_attendance',
                name='Can add attendance records',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_change_attendance',
                name='Can change attendance records',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_delete_attendance',
                name='Can delete attendance records',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_force_logout',
                name='Can force logout students',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_backdate_logout',
                name='Can backdate logout times',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_export_attendance',
                name='Can export attendance data',
                content_type=attendance_ct
            )[0],
            # Settings permissions
            Permission.objects.get_or_create(
                codename='can_manage_settings',
                name='Can manage attendance settings',
                content_type=settings_ct
            )[0],
        ]

        # Staff permissions (limited access)
        staff_permissions = [
            Permission.objects.get_or_create(
                codename='can_view_attendance',
                name='Can view attendance records',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_add_attendance',
                name='Can add attendance records',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_force_logout',
                name='Can force logout students',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_backdate_logout',
                name='Can backdate logout times',
                content_type=attendance_ct
            )[0],
            Permission.objects.get_or_create(
                codename='can_export_attendance',
                name='Can export attendance data',
                content_type=attendance_ct
            )[0],
        ]

        # Scanner-only permissions (minimal access)
        scanner_permissions = [
            Permission.objects.get_or_create(
                codename='can_add_attendance',
                name='Can add attendance records',
                content_type=attendance_ct
            )[0],
        ]

        # Assign permissions to groups
        admin_group.permissions.set(admin_permissions)
        staff_group.permissions.set(staff_permissions)
        scanner_only_group.permissions.set(scanner_permissions)

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully set up attendance groups:\n'
                f'- admin: {len(admin_permissions)} permissions\n'
                f'- staff: {len(staff_permissions)} permissions\n'
                f'- scanner_only: {len(scanner_permissions)} permissions'
            )
        )
