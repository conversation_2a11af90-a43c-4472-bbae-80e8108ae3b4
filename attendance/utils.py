"""
Utility functions for attendance module
"""

import base64
import json
from datetime import datetime, timedelta
from django.conf import settings
from django.core.signing import Signer, BadSignature
from django.utils import timezone
import qrcode
from io import BytesIO
import logging

logger = logging.getLogger(__name__)


class BackdatingValidator:
    """Utility class for validating backdated checkout times"""

    def __init__(self, max_backdate_hours=12):
        self.max_backdate_hours = max_backdate_hours

    def validate_backdate_time(self, attendance_record, checkout_time_str, current_time=None):
        """
        Validate backdated checkout time against all rules

        Args:
            attendance_record: AttendanceRecord instance
            checkout_time_str: Time string in "HH:MM" format
            current_time: Current datetime (for testing)

        Returns:
            dict: {'valid': bool, 'error': str, 'checkout_datetime': datetime}
        """
        if current_time is None:
            current_time = timezone.now()

        try:
            # Parse checkout time
            checkout_hour, checkout_minute = map(int, checkout_time_str.split(':'))
            checkout_datetime = attendance_record.scan_datetime.replace(
                hour=checkout_hour,
                minute=checkout_minute,
                second=0,
                microsecond=0
            )
        except ValueError:
            return {
                'valid': False,
                'error': 'Invalid time format. Use HH:MM (24-hour format)',
                'checkout_datetime': None
            }

        # Rule 1: Must be same day as check-in
        if checkout_datetime.date() != attendance_record.scan_date:
            return {
                'valid': False,
                'error': 'Checkout time must be on the same day as check-in',
                'checkout_datetime': None
            }

        # Rule 2: Must be after check-in time (minimum 1 minute)
        min_checkout_time = attendance_record.scan_datetime + timedelta(minutes=1)
        if checkout_datetime <= attendance_record.scan_datetime:
            return {
                'valid': False,
                'error': f'Checkout time must be after check-in time ({attendance_record.scan_datetime.strftime("%H:%M")})',
                'checkout_datetime': None
            }

        # Rule 3: Must not be in the future
        if checkout_datetime > current_time:
            return {
                'valid': False,
                'error': 'Checkout time cannot be in the future',
                'checkout_datetime': None
            }

        # Rule 4: Maximum backdating window
        time_diff = current_time - checkout_datetime
        if time_diff.total_seconds() > self.max_backdate_hours * 3600:
            return {
                'valid': False,
                'error': f'Cannot backdate more than {self.max_backdate_hours} hours ago',
                'checkout_datetime': None
            }

        # Rule 5: Reasonable session duration (max 24 hours)
        session_duration = checkout_datetime - attendance_record.scan_datetime
        if session_duration.total_seconds() > 24 * 3600:
            return {
                'valid': False,
                'error': 'Session duration cannot exceed 24 hours',
                'checkout_datetime': None
            }

        return {
            'valid': True,
            'error': None,
            'checkout_datetime': checkout_datetime
        }

    def get_allowed_time_range(self, attendance_record, current_time=None):
        """
        Get the allowed time range for backdating

        Returns:
            dict: {'min_time': str, 'max_time': str, 'current_time': str}
        """
        if current_time is None:
            current_time = timezone.now()

        # Minimum: 1 minute after check-in
        min_time = attendance_record.scan_datetime + timedelta(minutes=1)

        # Maximum: current time or max backdate window, whichever is more restrictive
        max_backdate_time = current_time - timedelta(hours=self.max_backdate_hours)
        max_time = max(min_time, max_backdate_time)
        max_time = min(max_time, current_time)

        return {
            'min_time': min_time.strftime('%H:%M'),
            'max_time': max_time.strftime('%H:%M'),
            'current_time': current_time.strftime('%H:%M'),
            'check_in_time': attendance_record.scan_datetime.strftime('%H:%M')
        }


# Create global instance
backdate_validator = BackdatingValidator()


class AuditLogger:
    """Utility class for logging attendance system actions"""

    @staticmethod
    def log_manual_checkout(user, attendance_record, reason, is_backdated=False, request=None):
        """Log manual checkout action"""
        from .models import AttendanceAuditLog

        action = 'backdated_checkout' if is_backdated else 'manual_checkout'

        original_data = {
            'status': 'present',
            'checkout_datetime': None,
            'is_manual_checkout': False,
            'is_backdated': False,
        }

        new_data = {
            'status': 'checked_out',
            'checkout_datetime': attendance_record.checkout_datetime.isoformat() if attendance_record.checkout_datetime else None,
            'is_manual_checkout': True,
            'is_backdated': is_backdated,
            'logout_reason': reason,
        }

        audit_log = AttendanceAuditLog.objects.create(
            action=action,
            performed_by=user,
            attendance_record=attendance_record,
            student=attendance_record.student,
            librarian=attendance_record.librarian,
            reason=reason,
            original_data=original_data,
            new_data=new_data,
            ip_address=get_client_ip(request) if request else None,
            user_agent=get_user_agent(request) if request else None,
        )

        logger.info(f"Audit log created: {action} for student {attendance_record.student.name} by {user.username}")
        return audit_log

    @staticmethod
    def log_attendance_edit(user, attendance_record, original_data, new_data, request=None):
        """Log attendance record edit"""
        from .models import AttendanceAuditLog

        audit_log = AttendanceAuditLog.objects.create(
            action='attendance_edit',
            performed_by=user,
            attendance_record=attendance_record,
            student=attendance_record.student,
            librarian=attendance_record.librarian,
            original_data=original_data,
            new_data=new_data,
            ip_address=get_client_ip(request) if request else None,
            user_agent=get_user_agent(request) if request else None,
        )

        logger.info(f"Audit log created: attendance_edit for student {attendance_record.student.name} by {user.username}")
        return audit_log

    @staticmethod
    def log_settings_change(user, setting_name, old_value, new_value, request=None):
        """Log settings change"""
        from .models import AttendanceAuditLog

        audit_log = AttendanceAuditLog.objects.create(
            action='settings_change',
            performed_by=user,
            original_data={'setting': setting_name, 'value': old_value},
            new_data={'setting': setting_name, 'value': new_value},
            ip_address=get_client_ip(request) if request else None,
            user_agent=get_user_agent(request) if request else None,
        )

        logger.info(f"Audit log created: settings_change for {setting_name} by {user.username}")
        return audit_log


# Create global instance
audit_logger = AuditLogger()


class AttendanceQRGenerator:
    """Handle QR code generation and validation for attendance"""
    
    def __init__(self):
        self.signer = Signer(salt='attendance_qr')
    
    def generate_student_qr(self, student, expiry_days=30):
        """
        Generate encrypted QR code for student attendance

        Args:
            student: StudentData instance
            expiry_days: Number of days until QR expires

        Returns:
            base64 encoded QR code image
        """
        try:
            # Create QR data with expiry
            expiry_date = timezone.now() + timedelta(days=expiry_days)
            qr_data = {
                'library_id': student.librarian.id,
                'student_id': student.id,
                'student_uuid': str(student.uuid) if hasattr(student, 'uuid') else None,
                'generated_at': timezone.now().isoformat(),
                'expires_at': expiry_date.isoformat(),
                'type': 'attendance'
            }
            
            # Convert to JSON and sign
            json_data = json.dumps(qr_data)
            signed_data = self.signer.sign(json_data)
            
            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(signed_data)
            qr.make(fit=True)
            
            # Create image
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            logger.info(f"Generated QR code for student {student.name} (ID: {student.id})")
            return qr_base64
            
        except Exception as e:
            logger.error(f"Failed to generate QR code for student {student.id}: {str(e)}")
            return None
    
    def validate_qr_content(self, qr_content):
        """
        Validate and decrypt QR code content
        
        Args:
            qr_content: The scanned QR code content
            
        Returns:
            dict: Validation result with student_id, status, and message
        """
        try:
            # Unsign the data
            unsigned_data = self.signer.unsign(qr_content)
            qr_data = json.loads(unsigned_data)
            
            # Validate QR type
            if qr_data.get('type') != 'attendance':
                return {
                    'valid': False,
                    'status': 'invalid',
                    'message': 'Invalid QR code type',
                    'student_id': None
                }
            
            # Check expiry
            expires_at = datetime.fromisoformat(qr_data['expires_at'].replace('Z', '+00:00'))
            if timezone.now() > expires_at:
                return {
                    'valid': False,
                    'status': 'expired',
                    'message': 'QR code has expired',
                    'student_id': qr_data.get('student_id'),
                    'qr_data': qr_data
                }
            
            # Valid QR code
            return {
                'valid': True,
                'status': 'present',
                'message': 'Valid QR code',
                'student_id': qr_data.get('student_id'),
                'qr_data': qr_data
            }
            
        except BadSignature:
            logger.warning(f"Invalid signature in QR code: {qr_content[:50]}...")
            return {
                'valid': False,
                'status': 'invalid',
                'message': 'Invalid or tampered QR code',
                'student_id': None
            }
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON in QR code: {qr_content[:50]}...")
            return {
                'valid': False,
                'status': 'invalid',
                'message': 'Corrupted QR code data',
                'student_id': None
            }
        except Exception as e:
            logger.error(f"Error validating QR code: {str(e)}")
            return {
                'valid': False,
                'status': 'invalid',
                'message': 'QR code validation failed',
                'student_id': None
            }


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_user_agent(request):
    """Get user agent from request"""
    return request.META.get('HTTP_USER_AGENT', '')


# Global instance
qr_generator = AttendanceQRGenerator()
