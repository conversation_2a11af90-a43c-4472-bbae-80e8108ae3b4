"""
Utility functions for attendance module
"""

import base64
import json
from datetime import datetime, timedelta
from django.conf import settings
from django.core.signing import Signer, BadSignature
from django.utils import timezone
import qrcode
from io import BytesIO
import logging

logger = logging.getLogger(__name__)


class AttendanceQRGenerator:
    """Handle QR code generation and validation for attendance"""
    
    def __init__(self):
        self.signer = Signer(salt='attendance_qr')
    
    def generate_student_qr(self, student, expiry_days=30):
        """
        Generate encrypted QR code for student attendance

        Args:
            student: StudentData instance
            expiry_days: Number of days until QR expires

        Returns:
            base64 encoded QR code image
        """
        try:
            # Create QR data with expiry
            expiry_date = timezone.now() + timedelta(days=expiry_days)
            qr_data = {
                'library_id': student.librarian.id,
                'student_id': student.id,
                'student_uuid': str(student.uuid) if hasattr(student, 'uuid') else None,
                'generated_at': timezone.now().isoformat(),
                'expires_at': expiry_date.isoformat(),
                'type': 'attendance'
            }
            
            # Convert to JSON and sign
            json_data = json.dumps(qr_data)
            signed_data = self.signer.sign(json_data)
            
            # Generate QR code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(signed_data)
            qr.make(fit=True)
            
            # Create image
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            logger.info(f"Generated QR code for student {student.name} (ID: {student.id})")
            return qr_base64
            
        except Exception as e:
            logger.error(f"Failed to generate QR code for student {student.id}: {str(e)}")
            return None
    
    def validate_qr_content(self, qr_content):
        """
        Validate and decrypt QR code content
        
        Args:
            qr_content: The scanned QR code content
            
        Returns:
            dict: Validation result with student_id, status, and message
        """
        try:
            # Unsign the data
            unsigned_data = self.signer.unsign(qr_content)
            qr_data = json.loads(unsigned_data)
            
            # Validate QR type
            if qr_data.get('type') != 'attendance':
                return {
                    'valid': False,
                    'status': 'invalid',
                    'message': 'Invalid QR code type',
                    'student_id': None
                }
            
            # Check expiry
            expires_at = datetime.fromisoformat(qr_data['expires_at'].replace('Z', '+00:00'))
            if timezone.now() > expires_at:
                return {
                    'valid': False,
                    'status': 'expired',
                    'message': 'QR code has expired',
                    'student_id': qr_data.get('student_id'),
                    'qr_data': qr_data
                }
            
            # Valid QR code
            return {
                'valid': True,
                'status': 'present',
                'message': 'Valid QR code',
                'student_id': qr_data.get('student_id'),
                'qr_data': qr_data
            }
            
        except BadSignature:
            logger.warning(f"Invalid signature in QR code: {qr_content[:50]}...")
            return {
                'valid': False,
                'status': 'invalid',
                'message': 'Invalid or tampered QR code',
                'student_id': None
            }
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON in QR code: {qr_content[:50]}...")
            return {
                'valid': False,
                'status': 'invalid',
                'message': 'Corrupted QR code data',
                'student_id': None
            }
        except Exception as e:
            logger.error(f"Error validating QR code: {str(e)}")
            return {
                'valid': False,
                'status': 'invalid',
                'message': 'QR code validation failed',
                'student_id': None
            }


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_user_agent(request):
    """Get user agent from request"""
    return request.META.get('HTTP_USER_AGENT', '')


# Global instance
qr_generator = AttendanceQRGenerator()
