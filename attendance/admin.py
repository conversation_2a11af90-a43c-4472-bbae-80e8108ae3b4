from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import AttendanceRecord, AttendanceSettings, AttendanceStatistics


@admin.register(AttendanceRecord)
class AttendanceRecordAdmin(admin.ModelAdmin):
    list_display = ['student_name', 'scan_date', 'scan_time', 'status', 'scanner_info', 'is_valid_qr']
    list_filter = ['status', 'scan_date', 'is_valid_qr', 'librarian', 'sublibrarian']
    search_fields = ['student__name', 'student__email', 'student__mobile']
    readonly_fields = ['scan_datetime', 'created_at', 'updated_at', 'qr_content']
    date_hierarchy = 'scan_date'

    fieldsets = (
        ('Student Information', {
            'fields': ('student', 'status')
        }),
        ('Scan Details', {
            'fields': ('scan_datetime', 'qr_content', 'is_valid_qr')
        }),
        ('Scanner Information', {
            'fields': ('scanned_by', 'librarian', 'sublibrarian')
        }),
        ('Technical Details', {
            'fields': ('ip_address', 'user_agent', 'notes'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def student_name(self, obj):
        return obj.student.name
    student_name.short_description = 'Student'
    student_name.admin_order_field = 'student__name'

    def scan_time(self, obj):
        return obj.scan_datetime.strftime('%H:%M:%S')
    scan_time.short_description = 'Time'

    def scanner_info(self, obj):
        return obj.scanner_name
    scanner_info.short_description = 'Scanned By'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'librarian', 'sublibrarian', 'scanned_by')


@admin.register(AttendanceSettings)
class AttendanceSettingsAdmin(admin.ModelAdmin):
    list_display = ['librarian', 'qr_expiry_days', 'allow_multiple_scans_per_day', 'success_sound_enabled']
    list_filter = ['allow_multiple_scans_per_day', 'success_sound_enabled', 'error_sound_enabled']
    search_fields = ['librarian__library_name']

    fieldsets = (
        ('Library', {
            'fields': ('librarian',)
        }),
        ('QR Code Settings', {
            'fields': ('qr_expiry_days', 'allow_multiple_scans_per_day')
        }),
        ('Audio/Visual Feedback', {
            'fields': ('success_sound_enabled', 'error_sound_enabled')
        }),
        ('Notifications', {
            'fields': ('notify_on_expired_scan', 'notify_on_invalid_qr')
        }),
    )


@admin.register(AttendanceStatistics)
class AttendanceStatisticsAdmin(admin.ModelAdmin):
    list_display = ['librarian', 'date', 'total_present', 'total_expired', 'total_invalid', 'unique_students', 'total_scans']
    list_filter = ['date', 'librarian']
    search_fields = ['librarian__library_name']
    readonly_fields = ['total_scans']
    date_hierarchy = 'date'

    def total_scans(self, obj):
        return obj.total_scans
    total_scans.short_description = 'Total Scans'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('librarian')
