from django.urls import path
from . import views

app_name = 'attendance'

urlpatterns = [
    # Scanner authentication
    path('scanner-login/', views.scanner_login, name='scanner_login'),
    path('scanner-logout/', views.scanner_logout, name='scanner_logout'),

    # Scanner page
    path('scanner/', views.scanner_page, name='scanner_page'),
    path('scanner/result/', views.scanner_result, name='scanner_result'),
    
    # Today's attendance
    path('today/', views.today_attendance, name='today_attendance'),
    path('today/students/<str:status>/', views.today_students_list, name='today_students_list'),
    
    # Attendance logs and reports
    path('log/', views.attendance_log, name='attendance_log'),
    path('export/', views.export_attendance, name='export_attendance'),
    
    # API endpoints
    path('api/scan/', views.api_scan_qr, name='api_scan_qr'),
    path('api/today-stats/', views.api_today_stats, name='api_today_stats'),
    path('api/live-students/', views.live_students_list, name='live_students_list'),
    path('api/checkout/', views.checkout_student, name='checkout_student'),
    path('api/backdate-checkout/', views.backdate_checkout, name='backdate_checkout'),
    
    # Settings
    path('settings/', views.attendance_settings, name='attendance_settings'),

    # Scanner Account Management
    path('manage-scanner-accounts/', views.manage_scanner_accounts, name='manage_scanner_accounts'),
    path('api/create-scanner-account/', views.create_scanner_account, name='create_scanner_account'),
    path('api/delete-scanner-account/', views.delete_scanner_account, name='delete_scanner_account'),
]
