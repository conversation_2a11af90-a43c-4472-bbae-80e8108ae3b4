from django.urls import path
from . import views

app_name = 'attendance'

urlpatterns = [
    # Scanner page
    path('scanner/', views.scanner_page, name='scanner_page'),
    path('scanner/result/', views.scanner_result, name='scanner_result'),
    
    # Today's attendance
    path('today/', views.today_attendance, name='today_attendance'),
    path('today/students/<str:status>/', views.today_students_list, name='today_students_list'),
    
    # Attendance logs and reports
    path('log/', views.attendance_log, name='attendance_log'),
    path('export/', views.export_attendance, name='export_attendance'),
    
    # API endpoints
    path('api/scan/', views.api_scan_qr, name='api_scan_qr'),
    path('api/today-stats/', views.api_today_stats, name='api_today_stats'),
    
    # Settings
    path('settings/', views.attendance_settings, name='attendance_settings'),
]
