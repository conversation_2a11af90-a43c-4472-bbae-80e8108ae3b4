"""
Signal handlers for attendance module
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.db.models import Count
from .models import AttendanceRecord, AttendanceStatistics
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=AttendanceRecord)
def update_attendance_statistics_on_save(sender, instance, created, **kwargs):
    """Update daily attendance statistics when a new record is created or updated"""
    try:
        # Get or create statistics for the day
        stats, created_stats = AttendanceStatistics.objects.get_or_create(
            librarian=instance.librarian or instance.sublibrarian.librarian,
            date=instance.scan_date,
            defaults={
                'total_present': 0,
                'total_expired': 0,
                'total_invalid': 0,
                'unique_students': 0,
            }
        )
        
        # Recalculate statistics for the day
        daily_records = AttendanceRecord.objects.filter(
            scan_date=instance.scan_date
        )
        
        if instance.librarian:
            daily_records = daily_records.filter(librarian=instance.librarian)
        else:
            daily_records = daily_records.filter(sublibrarian__librarian=instance.sublibrarian.librarian)
        
        # Count by status
        status_counts = daily_records.values('status').annotate(count=Count('id'))
        status_dict = {item['status']: item['count'] for item in status_counts}
        
        # Update statistics
        stats.total_present = status_dict.get('present', 0)
        stats.total_expired = status_dict.get('expired', 0)
        stats.total_invalid = status_dict.get('invalid', 0)
        stats.unique_students = daily_records.values('student').distinct().count()
        stats.save()
        
        logger.info(f"Updated attendance statistics for {stats.librarian.library_name} on {stats.date}")
        
    except Exception as e:
        logger.error(f"Failed to update attendance statistics: {str(e)}")


@receiver(post_delete, sender=AttendanceRecord)
def update_attendance_statistics_on_delete(sender, instance, **kwargs):
    """Update daily attendance statistics when a record is deleted"""
    try:
        # Find the statistics record
        librarian = instance.librarian or instance.sublibrarian.librarian
        stats = AttendanceStatistics.objects.filter(
            librarian=librarian,
            date=instance.scan_date
        ).first()
        
        if stats:
            # Recalculate statistics for the day
            daily_records = AttendanceRecord.objects.filter(
                scan_date=instance.scan_date
            )
            
            if instance.librarian:
                daily_records = daily_records.filter(librarian=instance.librarian)
            else:
                daily_records = daily_records.filter(sublibrarian__librarian=instance.sublibrarian.librarian)
            
            if daily_records.exists():
                # Count by status
                status_counts = daily_records.values('status').annotate(count=Count('id'))
                status_dict = {item['status']: item['count'] for item in status_counts}
                
                # Update statistics
                stats.total_present = status_dict.get('present', 0)
                stats.total_expired = status_dict.get('expired', 0)
                stats.total_invalid = status_dict.get('invalid', 0)
                stats.unique_students = daily_records.values('student').distinct().count()
                stats.save()
            else:
                # No records left for this day, delete the statistics
                stats.delete()
        
        logger.info(f"Updated attendance statistics after deletion for {librarian.library_name} on {instance.scan_date}")
        
    except Exception as e:
        logger.error(f"Failed to update attendance statistics on delete: {str(e)}")
