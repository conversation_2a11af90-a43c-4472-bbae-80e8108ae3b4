from django.test import TestCase
from django.utils import timezone
from django.contrib.auth.models import User, Group
from datetime import timedelta
import json

from .utils import qr_generator, backdate_validator
from .models import AttendanceRecord
from studentsData.models import StudentData
from librarian.models import Librarian_param


class AttendanceQRTestCase(TestCase):
    """Simple test case for QR code functionality without complex model dependencies"""

    def test_qr_code_validation_valid(self):
        """Test QR code validation with valid code"""
        from django.core.signing import Signer

        # Create valid QR data
        qr_data = {
            'student_id': 123,
            'generated_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timedelta(days=30)).isoformat(),
            'type': 'attendance'
        }

        signer = Signer(salt='attendance_qr')
        signed_content = signer.sign(json.dumps(qr_data))

        # Validate the QR content
        result = qr_generator.validate_qr_content(signed_content)

        self.assertTrue(result['valid'])
        self.assertEqual(result['status'], 'present')
        self.assertEqual(result['student_id'], 123)


class BackdatingValidationTestCase(TestCase):
    """Test case for backdating validation rules"""

    def setUp(self):
        # Create test user and librarian
        self.user = User.objects.create_user(username='testuser', password='testpass')
        self.librarian = Librarian_param.objects.create(
            user=self.user,
            library_name='Test Library',
            librarian_phone_num='1234567890',
            librarian_address='Test Address'
        )

        # Create test student
        self.student = StudentData.objects.create(
            name='Test Student',
            email='<EMAIL>',
            mobile='9876543210',
            librarian=self.librarian
        )

        # Create test attendance record
        self.attendance = AttendanceRecord.objects.create(
            student=self.student,
            librarian=self.librarian,
            scanned_by=self.user,
            scan_datetime=timezone.now() - timedelta(hours=2),
            status='present'
        )

    def test_valid_backdate_time(self):
        """Test valid backdating within allowed window"""
        # 1 hour after check-in
        checkout_time = (self.attendance.scan_datetime + timedelta(hours=1)).strftime('%H:%M')
        result = backdate_validator.validate_backdate_time(self.attendance, checkout_time)

        self.assertTrue(result['valid'])
        self.assertIsNone(result['error'])
        self.assertIsNotNone(result['checkout_datetime'])

    def test_backdate_before_checkin(self):
        """Test backdating before check-in time (should fail)"""
        # 30 minutes before check-in
        checkout_time = (self.attendance.scan_datetime - timedelta(minutes=30)).strftime('%H:%M')
        result = backdate_validator.validate_backdate_time(self.attendance, checkout_time)

        self.assertFalse(result['valid'])
        self.assertIn('after check-in time', result['error'])

    def test_backdate_future_time(self):
        """Test backdating to future time (should fail)"""
        # 1 hour in the future
        future_time = (timezone.now() + timedelta(hours=1)).strftime('%H:%M')
        result = backdate_validator.validate_backdate_time(self.attendance, future_time)

        self.assertFalse(result['valid'])
        self.assertIn('cannot be in the future', result['error'])

    def test_backdate_too_old(self):
        """Test backdating beyond allowed window (should fail)"""
        # Create attendance record from 15 hours ago
        old_attendance = AttendanceRecord.objects.create(
            student=self.student,
            librarian=self.librarian,
            scanned_by=self.user,
            scan_datetime=timezone.now() - timedelta(hours=15),
            status='present'
        )

        # Try to backdate to 14 hours ago (beyond 12-hour limit)
        checkout_time = (timezone.now() - timedelta(hours=14)).strftime('%H:%M')
        result = backdate_validator.validate_backdate_time(old_attendance, checkout_time)

        self.assertFalse(result['valid'])
        self.assertIn('Cannot backdate more than', result['error'])

    def test_invalid_time_format(self):
        """Test invalid time format (should fail)"""
        result = backdate_validator.validate_backdate_time(self.attendance, 'invalid-time')

        self.assertFalse(result['valid'])
        self.assertIn('Invalid time format', result['error'])

    def test_qr_code_validation_expired(self):
        """Test QR code validation with expired code"""
        from django.core.signing import Signer

        # Create expired QR data
        qr_data = {
            'student_id': 123,
            'generated_at': (timezone.now() - timedelta(days=35)).isoformat(),
            'expires_at': (timezone.now() - timedelta(days=5)).isoformat(),  # Expired 5 days ago
            'type': 'attendance'
        }

        signer = Signer(salt='attendance_qr')
        signed_content = signer.sign(json.dumps(qr_data))

        # Validate the QR content
        result = qr_generator.validate_qr_content(signed_content)

        self.assertFalse(result['valid'])
        self.assertEqual(result['status'], 'expired')
        self.assertEqual(result['student_id'], 123)

    def test_qr_code_validation_invalid(self):
        """Test QR code validation with invalid/tampered code"""
        # Test with completely invalid content
        result = qr_generator.validate_qr_content('invalid_qr_content')

        self.assertFalse(result['valid'])
        self.assertEqual(result['status'], 'invalid')
        self.assertIsNone(result['student_id'])

    def test_qr_code_validation_wrong_type(self):
        """Test QR code validation with wrong type"""
        from django.core.signing import Signer

        # Create QR data with wrong type
        qr_data = {
            'student_id': 123,
            'generated_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timedelta(days=30)).isoformat(),
            'type': 'registration'  # Wrong type
        }

        signer = Signer(salt='attendance_qr')
        signed_content = signer.sign(json.dumps(qr_data))

        # Validate the QR content
        result = qr_generator.validate_qr_content(signed_content)

        self.assertFalse(result['valid'])
        self.assertEqual(result['status'], 'invalid')
        self.assertIsNone(result['student_id'])
