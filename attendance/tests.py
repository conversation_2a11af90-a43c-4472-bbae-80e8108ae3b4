from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
import json

from .utils import qr_generator


class AttendanceQRTestCase(TestCase):
    """Simple test case for QR code functionality without complex model dependencies"""

    def test_qr_code_validation_valid(self):
        """Test QR code validation with valid code"""
        from django.core.signing import Signer

        # Create valid QR data
        qr_data = {
            'student_id': 123,
            'generated_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timedelta(days=30)).isoformat(),
            'type': 'attendance'
        }

        signer = Signer(salt='attendance_qr')
        signed_content = signer.sign(json.dumps(qr_data))

        # Validate the QR content
        result = qr_generator.validate_qr_content(signed_content)

        self.assertTrue(result['valid'])
        self.assertEqual(result['status'], 'present')
        self.assertEqual(result['student_id'], 123)

    def test_qr_code_validation_expired(self):
        """Test QR code validation with expired code"""
        from django.core.signing import Signer

        # Create expired QR data
        qr_data = {
            'student_id': 123,
            'generated_at': (timezone.now() - timedelta(days=35)).isoformat(),
            'expires_at': (timezone.now() - timedelta(days=5)).isoformat(),  # Expired 5 days ago
            'type': 'attendance'
        }

        signer = Signer(salt='attendance_qr')
        signed_content = signer.sign(json.dumps(qr_data))

        # Validate the QR content
        result = qr_generator.validate_qr_content(signed_content)

        self.assertFalse(result['valid'])
        self.assertEqual(result['status'], 'expired')
        self.assertEqual(result['student_id'], 123)

    def test_qr_code_validation_invalid(self):
        """Test QR code validation with invalid/tampered code"""
        # Test with completely invalid content
        result = qr_generator.validate_qr_content('invalid_qr_content')

        self.assertFalse(result['valid'])
        self.assertEqual(result['status'], 'invalid')
        self.assertIsNone(result['student_id'])

    def test_qr_code_validation_wrong_type(self):
        """Test QR code validation with wrong type"""
        from django.core.signing import Signer

        # Create QR data with wrong type
        qr_data = {
            'student_id': 123,
            'generated_at': timezone.now().isoformat(),
            'expires_at': (timezone.now() + timedelta(days=30)).isoformat(),
            'type': 'registration'  # Wrong type
        }

        signer = Signer(salt='attendance_qr')
        signed_content = signer.sign(json.dumps(qr_data))

        # Validate the QR content
        result = qr_generator.validate_qr_content(signed_content)

        self.assertFalse(result['valid'])
        self.assertEqual(result['status'], 'invalid')
        self.assertIsNone(result['student_id'])
