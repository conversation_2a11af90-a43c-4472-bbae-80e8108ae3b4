# Scanner-Only Access & Live Attendance System

## Overview

This system provides a comprehensive attendance management solution with role-based access control, live attendance tracking, and backdated logout functionality.

## Features

### 1. Scanner-Only Access System
- **New Login Page**: Dedicated login for scanner operators at `/attendance/scanner-login/`
- **Role-Based Access**: Three user roles with different permissions:
  - `admin`: Full access to all features
  - `staff`: Limited reports, can see Today page, can manage checkouts
  - `scanner_only`: Can only access scanner functionality

### 2. Live Attendance Tracking
- **"Live Right Now" Card**: Shows count of students currently present
- **Real-time Updates**: Automatic refresh every 30 seconds
- **Interactive List**: Click to view all currently present students
- **Visual Indicators**: Blinking green dot for "LIVE" status

### 3. Check-Out System
- **Immediate Logout**: Quick logout with reason selection
- **Backdated Logout**: Logout with custom time and mandatory reason
- **Reason Tracking**: Four predefined reasons:
  1. Forgot to Scan Out
  2. Left Early (Emergency)
  3. System/Scanner Error
  4. Manual Correction

### 4. Backdating Rules & Validation
- **Time Window**: Maximum 12 hours backdating (configurable)
- **Same Day Only**: Checkout must be on same day as check-in
- **After Check-in**: Checkout time must be after check-in time
- **No Future Times**: Cannot backdate to future times
- **Mandatory Reason**: Reason selection required for all backdated logouts

### 5. Audit Logging System
- **Comprehensive Tracking**: All manual actions are logged
- **User Attribution**: Records who performed each action
- **Data Preservation**: Stores original and new data for changes
- **IP & User Agent**: Tracks request metadata for security

## URL Structure

```
/attendance/scanner-login/          # Scanner-only login page
/attendance/scanner-logout/         # Scanner logout
/attendance/scanner/                # QR scanner page
/attendance/today/                  # Today's attendance overview
/attendance/api/live-students/      # API: Get currently present students
/attendance/api/checkout/           # API: Immediate checkout
/attendance/api/backdate-checkout/  # API: Backdated checkout
/attendance/api/today-stats/        # API: Today's statistics with live count
```

## User Roles & Permissions

### Admin Role
- Full access to all attendance features
- Can view all reports and statistics
- Can force logout and backdate times
- Can manage attendance settings
- Can export data

### Staff Role
- Can view attendance records
- Can access Today page
- Can force logout and backdate times
- Can export data
- Limited dashboard access

### Scanner-Only Role
- Can only access scanner page
- Can scan QR codes for attendance
- Cannot access reports or other features
- Automatically redirected to scanner

## Security Features

### Permission Middleware
- `AttendancePermissionMiddleware`: Enforces URL access restrictions
- `ScannerOnlyRedirectMiddleware`: Redirects scanner-only users appropriately

### Backdating Validation
- Strict time window enforcement
- Business rule validation
- Audit trail for all changes

### Access Control
- Role-based URL restrictions
- Permission-based feature access
- Secure authentication flow

## Setup Instructions

### 1. Run Migrations
```bash
python manage.py makemigrations attendance
python manage.py migrate
```

### 2. Set Up User Groups
```bash
python manage.py setup_attendance_groups
```

### 3. Configure Middleware (Optional)
Add to `settings.py`:
```python
MIDDLEWARE = [
    # ... existing middleware ...
    'attendance.middleware.AttendancePermissionMiddleware',
    'attendance.middleware.ScannerOnlyRedirectMiddleware',
]
```

### 4. Create Users
- Create users and assign to appropriate groups:
  - `admin` group for full access
  - `staff` group for limited access  
  - `scanner_only` group for scanner access only

## API Endpoints

### Live Students List
```
GET /attendance/api/live-students/
Response: {
    "students": [
        {
            "id": 1,
            "student_name": "John Doe",
            "student_unique_id": "STU001",
            "check_in_time": "09:30",
            "duration": "2h 30m"
        }
    ],
    "count": 1
}
```

### Immediate Checkout
```
POST /attendance/api/checkout/
Body: {
    "attendance_id": 1,
    "reason": "forgot_scan"
}
Response: {
    "success": true,
    "message": "Successfully checked out John Doe",
    "checkout_time": "12:00:00"
}
```

### Backdated Checkout
```
POST /attendance/api/backdate-checkout/
Body: {
    "attendance_id": 1,
    "checkout_time": "11:30",
    "reason": "left_early",
    "notes": "Family emergency"
}
Response: {
    "success": true,
    "message": "Successfully backdated checkout for John Doe",
    "checkout_time": "11:30:00"
}
```

## Configuration

### Backdating Settings
- Maximum backdate hours: 12 (configurable in `BackdatingValidator`)
- Minimum session duration: 1 minute
- Maximum session duration: 24 hours

### Audio Settings
- Success/failure sounds with fallback beep generation
- Configurable per library in `AttendanceSettings`

### Caching
- API responses cached for 5 minutes
- Live count auto-refresh every 30 seconds
- Service worker caching for offline support

## Testing

Run the test suite:
```bash
python manage.py test attendance
```

Tests cover:
- QR code validation
- Backdating rule enforcement
- Permission checking
- API endpoint functionality

## Troubleshooting

### Common Issues

1. **Scanner-only users can't access other pages**
   - This is by design. Check user group assignment.

2. **Backdating fails with time validation errors**
   - Verify time is within allowed window and after check-in time.

3. **Live count not updating**
   - Check API endpoint permissions and network connectivity.

4. **Audio not playing**
   - Browser may block autoplay. System includes fallback beep generation.

### Debug Mode
Enable detailed logging by setting:
```python
LOGGING = {
    'loggers': {
        'attendance': {
            'level': 'DEBUG',
        }
    }
}
```
