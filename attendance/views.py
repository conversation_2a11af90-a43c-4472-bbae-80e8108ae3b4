from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.utils import timezone
from django.db.models import Count, Q
from django.core.paginator import <PERSON><PERSON><PERSON>
from datetime import datetime, timedelta
import json
import logging

from studentsData.models import StudentData
from librarian.models import Librarian_param
from subLibrarian.models import Sublibrarian_param
from .models import AttendanceRecord, AttendanceSettings, AttendanceStatistics
from .utils import qr_generator, get_client_ip, get_user_agent

logger = logging.getLogger(__name__)


@login_required(login_url="/librarian/login/")
def scanner_page(request):
    """Display the QR code scanner page"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get or create attendance settings
    try:
        if role == 'Librarian':
            librarian = Librarian_param.objects.get(user=request.user)
            settings, created = AttendanceSettings.objects.get_or_create(librarian=librarian)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            settings, created = AttendanceSettings.objects.get_or_create(librarian=sublibrarian.librarian)
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        settings = None

    context = {
        'role': role,
        'settings': settings,
    }

    return render(request, 'attendance/scanner.html', context)


@login_required(login_url="/librarian/login/")
@require_http_methods(["POST"])
def scanner_result(request):
    """Process scanned QR code and return result page"""
    qr_content = request.POST.get('qr_content', '').strip()

    if not qr_content:
        return render(request, 'attendance/scanner_result.html', {
            'status': 'error',
            'message': 'No QR code content received',
            'background_color': 'red'
        })

    # Validate QR code
    validation_result = qr_generator.validate_qr_content(qr_content)

    # Get user info
    role = request.user.groups.first().name if request.user.groups.exists() else None
    librarian = None
    sublibrarian = None

    try:
        if role == 'Librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        return render(request, 'attendance/scanner_result.html', {
            'status': 'error',
            'message': 'User not authorized for attendance scanning',
            'background_color': 'red'
        })

    # Process the result
    if validation_result['valid']:
        # Get student with optimized query using library_id from QR
        try:
            qr_library_id = validation_result.get('qr_data', {}).get('library_id')

            # Verify library_id matches current library for security
            if qr_library_id and qr_library_id != librarian.id:
                return render(request, 'attendance/scanner_result.html', {
                    'status': 'error',
                    'message': 'QR code is for a different library',
                    'background_color': 'red'
                })

            # Optimized query: get student and verify library in single query
            student = StudentData.objects.select_related('librarian').get(
                id=validation_result['student_id'],
                librarian=librarian
            )

            # Check for existing attendance today
            today = timezone.now().date()
            existing_attendance = AttendanceRecord.objects.filter(
                student=student,
                scan_date=today
            ).first()

            if existing_attendance:
                return render(request, 'attendance/scanner_result.html', {
                    'status': 'warning',
                    'message': f'Attendance already marked for {student.name} today',
                    'student': student,
                    'background_color': 'orange'
                })

            # Create attendance record
            attendance = AttendanceRecord.objects.create(
                student=student,
                librarian=librarian if role == 'Librarian' else None,
                sublibrarian=sublibrarian,
                scanned_by=request.user,
                status='present',
                qr_content=qr_content,
                is_valid_qr=True,
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request)
            )

            return render(request, 'attendance/scanner_result.html', {
                'status': 'success',
                'message': f'Attendance marked successfully for {student.name}',
                'student': student,
                'attendance': attendance,
                'background_color': 'green'
            })

        except StudentData.DoesNotExist:
            return render(request, 'attendance/scanner_result.html', {
                'status': 'error',
                'message': 'Student not found',
                'background_color': 'red'
            })

    else:
        # Handle invalid/expired QR codes
        student = None
        if validation_result.get('student_id'):
            try:
                student = StudentData.objects.get(id=validation_result['student_id'])
            except StudentData.DoesNotExist:
                pass

        # Create attendance record for tracking
        AttendanceRecord.objects.create(
            student=student,
            librarian=librarian if role == 'Librarian' else None,
            sublibrarian=sublibrarian,
            scanned_by=request.user,
            status=validation_result['status'],
            qr_content=qr_content,
            is_valid_qr=False,
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request),
            notes=validation_result['message']
        )

        background_color = 'red' if validation_result['status'] == 'invalid' else 'orange'

        return render(request, 'attendance/scanner_result.html', {
            'status': validation_result['status'],
            'message': validation_result['message'],
            'student': student,
            'background_color': background_color
        })


@login_required(login_url="/librarian/login/")
def today_attendance(request):
    """Display today's attendance summary"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get librarian
    try:
        if role == 'Librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        messages.error(request, "User not authorized for attendance viewing")
        return redirect('/')

    today = timezone.now().date()

    # Get today's attendance records
    today_records = AttendanceRecord.objects.filter(
        scan_date=today
    ).select_related('student')

    if role == 'Librarian':
        today_records = today_records.filter(librarian=librarian)
    else:
        today_records = today_records.filter(sublibrarian__librarian=librarian)

    # Group by status
    present_students = today_records.filter(status='present')
    expired_students = today_records.filter(status='expired')
    invalid_students = today_records.filter(status='invalid')

    # Get statistics
    stats = {
        'total_present': present_students.count(),
        'total_expired': expired_students.count(),
        'total_invalid': invalid_students.count(),
        'total_scans': today_records.count(),
    }

    context = {
        'role': role,
        'today': today,
        'stats': stats,
        'present_students': present_students,
        'expired_students': expired_students,
        'invalid_students': invalid_students,
    }

    return render(request, 'attendance/today.html', context)


@login_required(login_url="/librarian/login/")
def today_students_list(request, status):
    """API endpoint to get students list for a specific status"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get librarian
    try:
        if role == 'Librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    today = timezone.now().date()

    # Get records for the status
    records = AttendanceRecord.objects.filter(
        scan_date=today,
        status=status
    ).select_related('student')

    if role == 'Librarian':
        records = records.filter(librarian=librarian)
    else:
        records = records.filter(sublibrarian__librarian=librarian)

    # Format data
    students_data = []
    for record in records:
        students_data.append({
            'name': record.student.name,
            'unique_id': record.student.unique_id,
            'email': record.student.email,
            'scan_time': record.scan_datetime.strftime('%H:%M:%S'),
            'status': record.status
        })

    return JsonResponse({
        'students': students_data,
        'count': len(students_data)
    })


@login_required(login_url="/librarian/login/")
def attendance_log(request):
    """Display attendance logs with filtering and statistics"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get librarian
    try:
        if role == 'Librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        messages.error(request, "User not authorized for attendance viewing")
        return redirect('/')

    # Get date range from request
    date_range = request.GET.get('range', '30')  # Default 30 days
    end_date = timezone.now().date()

    if date_range == '30':
        start_date = end_date - timedelta(days=30)
    elif date_range == '90':
        start_date = end_date - timedelta(days=90)
    elif date_range == '180':
        start_date = end_date - timedelta(days=180)
    elif date_range == '365':
        start_date = end_date - timedelta(days=365)
    elif date_range == 'custom':
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else end_date - timedelta(days=30)
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date() if end_date_str else end_date
        except ValueError:
            start_date = end_date - timedelta(days=30)
    else:
        start_date = end_date - timedelta(days=30)

    # Get attendance records
    records = AttendanceRecord.objects.filter(
        scan_date__range=[start_date, end_date]
    ).select_related('student').order_by('-scan_datetime')

    if role == 'Librarian':
        records = records.filter(librarian=librarian)
    else:
        records = records.filter(sublibrarian__librarian=librarian)

    # Pagination
    paginator = Paginator(records, 50)  # 50 records per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate statistics
    total_records = records.count()
    present_count = records.filter(status='present').count()
    expired_count = records.filter(status='expired').count()
    invalid_count = records.filter(status='invalid').count()
    unique_students = records.values('student').distinct().count()

    # Most frequent visitor
    most_frequent = records.filter(status='present').values('student__name', 'student__unique_id').annotate(
        visit_count=Count('id')
    ).order_by('-visit_count').first()

    # Day with max attendance
    max_attendance_day = records.filter(status='present').values('scan_date').annotate(
        daily_count=Count('id')
    ).order_by('-daily_count').first()

    # Students with multiple visits (potential overuse)
    overused_students = records.filter(status='present').values(
        'student__name', 'student__unique_id'
    ).annotate(
        visit_count=Count('id')
    ).filter(visit_count__gt=20).order_by('-visit_count')  # More than 20 visits in period

    context = {
        'role': role,
        'page_obj': page_obj,
        'start_date': start_date,
        'end_date': end_date,
        'date_range': date_range,
        'stats': {
            'total_records': total_records,
            'present_count': present_count,
            'expired_count': expired_count,
            'invalid_count': invalid_count,
            'unique_students': unique_students,
            'most_frequent': most_frequent,
            'max_attendance_day': max_attendance_day,
            'overused_students': overused_students,
        }
    }

    return render(request, 'attendance/log.html', context)


@login_required(login_url="/librarian/login/")
def export_attendance(request):
    """Export attendance data to Excel"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get librarian
    try:
        if role == 'Librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    # Get date range from request
    date_range = request.GET.get('range', '30')
    end_date = timezone.now().date()

    if date_range == '30':
        start_date = end_date - timedelta(days=30)
    elif date_range == '90':
        start_date = end_date - timedelta(days=90)
    elif date_range == '180':
        start_date = end_date - timedelta(days=180)
    elif date_range == '365':
        start_date = end_date - timedelta(days=365)
    elif date_range == 'custom':
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else end_date - timedelta(days=30)
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date() if end_date_str else end_date
        except ValueError:
            start_date = end_date - timedelta(days=30)
    else:
        start_date = end_date - timedelta(days=30)

    # Get attendance records
    records = AttendanceRecord.objects.filter(
        scan_date__range=[start_date, end_date]
    ).select_related('student').order_by('-scan_datetime')

    if role == 'Librarian':
        records = records.filter(librarian=librarian)
    else:
        records = records.filter(sublibrarian__librarian=librarian)

    # Create Excel response using openpyxl
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        from openpyxl.utils import get_column_letter

        # Create workbook and worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Attendance Report"

        # Headers
        headers = [
            'Date', 'Time', 'Student Name', 'Student ID', 'Email', 'Status',
            'Scanned By', 'IP Address', 'Notes'
        ]

        # Style headers
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # Add data
        for row, record in enumerate(records, 2):
            ws.cell(row=row, column=1, value=record.scan_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=2, value=record.scan_datetime.strftime('%H:%M:%S'))
            ws.cell(row=row, column=3, value=record.student.name if record.student else 'Unknown')
            ws.cell(row=row, column=4, value=record.student.unique_id if record.student else 'N/A')
            ws.cell(row=row, column=5, value=record.student.email if record.student else 'N/A')
            ws.cell(row=row, column=6, value=record.status.title())
            ws.cell(row=row, column=7, value=record.scanned_by.username if record.scanned_by else 'System')
            ws.cell(row=row, column=8, value=record.ip_address or 'N/A')
            ws.cell(row=row, column=9, value=record.notes or '')

        # Auto-adjust column widths
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].auto_size = True

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="attendance_{start_date}_to_{end_date}.xlsx"'

        wb.save(response)

    except ImportError:
        # Fallback to CSV if openpyxl is not available
        import csv
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="attendance_{start_date}_to_{end_date}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Date', 'Time', 'Student Name', 'Student ID', 'Email', 'Status',
            'Scanned By', 'IP Address', 'Notes'
        ])

        for record in records:
            writer.writerow([
                record.scan_date.strftime('%Y-%m-%d'),
                record.scan_datetime.strftime('%H:%M:%S'),
                record.student.name if record.student else 'Unknown',
                record.student.unique_id if record.student else 'N/A',
                record.student.email if record.student else 'N/A',
                record.status.title(),
                record.scanned_by.username if record.scanned_by else 'System',
                record.ip_address or 'N/A',
                record.notes or ''
            ])

    return response


@login_required(login_url="/librarian/login/")
@csrf_exempt
def api_scan_qr(request):
    """API endpoint for QR code scanning"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        data = json.loads(request.body)
        qr_content = data.get('qr_content', '').strip()

        if not qr_content:
            return JsonResponse({'error': 'No QR content provided'}, status=400)

        # Validate QR code
        validation_result = qr_generator.validate_qr_content(qr_content)

        return JsonResponse({
            'valid': validation_result['valid'],
            'status': validation_result['status'],
            'message': validation_result['message'],
            'student_id': validation_result.get('student_id')
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        logger.error(f"API scan error: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@login_required(login_url="/librarian/login/")
def api_today_stats(request):
    """API endpoint for today's attendance statistics with caching"""
    from django.core.cache import cache

    role = request.user.groups.first().name if request.user.groups.exists() else None

    try:
        if role == 'Librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    today = timezone.now().date()
    cache_key = f"attendance_stats_{librarian.id}_{today}"

    # Try to get from cache first (5 minute cache)
    cached_stats = cache.get(cache_key)
    if cached_stats:
        return JsonResponse(cached_stats)

    # Get today's statistics
    stats = AttendanceStatistics.objects.filter(
        librarian=librarian,
        date=today
    ).first()

    if stats:
        response_data = {
            'date': today.isoformat(),
            'total_present': stats.total_present,
            'total_expired': stats.total_expired,
            'total_invalid': stats.total_invalid,
            'unique_students': stats.unique_students,
            'total_scans': stats.total_scans
        }
    else:
        response_data = {
            'date': today.isoformat(),
            'total_expired': 0,
            'total_invalid': 0,
            'unique_students': 0,
            'total_scans': 0
        }

    # Cache the response for 5 minutes
    cache.set(cache_key, response_data, 300)
    return JsonResponse(response_data)


@login_required(login_url="/librarian/login/")
def attendance_settings(request):
    """Attendance settings page"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    try:
        if role == 'Librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        messages.error(request, "User not authorized")
        return redirect('/')

    settings, created = AttendanceSettings.objects.get_or_create(librarian=librarian)

    if request.method == 'POST':
        # Update settings
        settings.qr_expiry_days = int(request.POST.get('qr_expiry_days', 30))
        settings.allow_multiple_scans_per_day = request.POST.get('allow_multiple_scans_per_day') == 'on'
        settings.success_sound_enabled = request.POST.get('success_sound_enabled') == 'on'
        settings.error_sound_enabled = request.POST.get('error_sound_enabled') == 'on'
        settings.notify_on_expired_scan = request.POST.get('notify_on_expired_scan') == 'on'
        settings.notify_on_invalid_qr = request.POST.get('notify_on_invalid_qr') == 'on'
        settings.save()

        messages.success(request, 'Attendance settings updated successfully!')
        return redirect('attendance:attendance_settings')

    context = {
        'role': role,
        'settings': settings,
    }

    return render(request, 'attendance/settings.html', context)
