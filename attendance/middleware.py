from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages
from django.http import HttpResponseForbidden


class AttendancePermissionMiddleware:
    """
    Middleware to enforce attendance system permissions and URL access restrictions
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Define allowed URLs for each role
        self.scanner_only_allowed_urls = [
            '/attendance/scanner/',
            '/attendance/scanner/result/',
            '/attendance/api/scan/',
            '/attendance/scanner-login/',
            '/attendance/scanner-logout/',
            '/static/',  # Allow static files
            '/media/',   # Allow media files
        ]
        
        self.staff_allowed_urls = [
            '/attendance/',  # All attendance URLs
            '/librarian/dashboard/',  # Limited dashboard access
            '/static/',
            '/media/',
        ]
        
        # URLs that require specific permissions
        self.admin_only_urls = [
            '/attendance/settings/',
            '/librarian/shifts/',
            '/librarian/students/',
            '/librarian/invoices/',
        ]

    def __call__(self, request):
        # Skip middleware for non-authenticated users
        if not request.user.is_authenticated:
            response = self.get_response(request)
            return response
        
        # Get user's role
        user_groups = request.user.groups.values_list('name', flat=True)
        
        # Check if user has scanner_only role
        if 'scanner_only' in user_groups:
            # Restrict scanner_only users to specific URLs
            if not any(request.path.startswith(url) for url in self.scanner_only_allowed_urls):
                messages.error(request, 'Access denied. Scanner-only users can only access the scanner.')
                return redirect('attendance:scanner_page')
        
        # Check admin-only URLs
        elif any(request.path.startswith(url) for url in self.admin_only_urls):
            if 'admin' not in user_groups:
                messages.error(request, 'Access denied. Admin privileges required.')
                return HttpResponseForbidden('Access denied. Admin privileges required.')
        
        response = self.get_response(request)
        return response


class ScannerOnlyRedirectMiddleware:
    """
    Middleware to redirect scanner_only users to appropriate pages
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if user is scanner_only and trying to access root or dashboard
        if (request.user.is_authenticated and 
            request.user.groups.filter(name='scanner_only').exists()):
            
            # Redirect from root or dashboard to scanner
            if request.path in ['/', '/librarian/dashboard/', '/sublibrarian/dashboard/']:
                return redirect('attendance:scanner_page')
        
        response = self.get_response(request)
        return response
