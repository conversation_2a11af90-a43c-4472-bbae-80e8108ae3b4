from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from Library.views import send_sms
import threading, requests, os

def send_dynamic_email(subject, template_name, context, to_email):
    from_email = settings.EMAIL_HOST_USER

    try:
        # Render the HTML template and convert it to plain text
        html_content = render_to_string(template_name, context)
        text_content = strip_tags(html_content)

        # Use threading to send email asynchronously
        email_thread = threading.Thread(
            target=send_email_thread,
            args=(subject, from_email, to_email, html_content, text_content),
        )
        email_thread.start()

    except Exception as e:
        raise e


def send_email_thread(subject, from_email, to_email, html_content, text_content):
    try:
        email = EmailMultiAlternatives(
            subject=subject,
            body=text_content,
            from_email=from_email,
            to=[to_email],
        )

        email.attach_alternative(html_content, "text/html")
        result = email.send()

    except Exception as e:
        error_message = str(e)

        # Check if it's an SSL certificate error
        if "CERTIFICATE_VERIFY_FAILED" in error_message or "SSL" in error_message:
            # Try to send with console backend as fallback for development
            try:
                from django.core.mail.backends.console import EmailBackend
                console_backend = EmailBackend()
                console_backend.send_messages([email])
            except Exception as console_error:
                pass


def send_email_async(subject, html_content, from_email, to_email):
    try:
        msg = EmailMultiAlternatives(
            subject, strip_tags(html_content), from_email, [to_email]
        )
        msg.attach_alternative(html_content, "text/html")
        msg.send()
    except Exception as e:
        pass

template_content = {
        "services fees":{
            "template_id": 173593,
            "content": "Dear {#var#}, Your library fee at {#var#} is due. Please pay today to continue enjoying our services and resources. LBRIAN",
            },
        "library fee":{
            "template_id": 173446,
            "content": "Dear, {#var#} Your library fee is due. Please pay today to continue enjoying our services and resources. LBRIAN",
            },
        "fee_due_today":{
            "template_id": 173446,
            "content": "Dear {#var#}, Your library fee at {#var#} is DUE TODAY ({#var#}). Please pay immediately to avoid service interruption. Student ID: {#var#}. LBRIAN",
            },
        "fee_overdue":{
            "template_id": 173593,
            "content": "URGENT: Dear {#var#}, Your library fee at {#var#} is {#var#} days OVERDUE (Due: {#var#}). Pay immediately to avoid suspension. Student ID: {#var#}. LBRIAN",
            },
        "fee_long_overdue":{
            "template_id": 173593,
            "content": "FINAL NOTICE: Dear {#var#}, Your library fee at {#var#} is {#var#} days overdue. Contact us immediately to avoid account suspension. Student ID: {#var#}. LBRIAN",
            },
        "fee_reminder":{
            "template_id": 173446,
            "content": "Dear {#var#}, Reminder: Your library fee at {#var#} is due on {#var#} ({#var#} days remaining). Please pay on time. Student ID: {#var#}. LBRIAN",
            },
        "special discount":{
            "template_id": 173385,
            "content": "Hi Student, enjoy a special discount on your {#var#} examination forms at {#var#}. Apply now and save {#var#} before {#var#} LBRIAN",
            },
        "Library Subscription":{
            "template_id": 170593,
            "content": "Dear Customer, Your Library Subscription of Rs {#var#} has been generated. Check details: {#var#}{#var#} LBRIAN",
            },
        "partner in growth":{ #Sms to be send when user register on librainian app
            "template_id": 165731,
            "content": "Hi {#var#}, Thank you so much for choosing Librainian App – Your partner in growth, Your OTP to register you Library is: {#var#}. PNKVEN",
            },
}


def format_sms_message(template_content, *args):
    """
    Format SMS message by replacing {#var#} placeholders with actual values
    """
    message = template_content
    for arg in args:
        message = message.replace("{#var#}", str(arg), 1)
    return message


def send_bulk_sms(numbers, message, template_id):
    url = "https://www.bulksmsplans.com/api/send_sms"  # SMS API URL
    payload = {
        "api_id": settings.BULKSMS_API_ID,
        "api_password": settings.BULKSMS_API_PASSWORD,
        "sms_type": "Transactional",
        "sms_encoding": "3",
        "sender": settings.BULKSMS_SENDER,
        "number": numbers,  # Join multiple numbers with a comma
        "message": message,
        "template_id": template_id
    }

    headers = {}
    try:
        response = requests.post(url, data=payload, headers=headers)
        return response.json()  # Assuming API returns JSON response
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}


    


