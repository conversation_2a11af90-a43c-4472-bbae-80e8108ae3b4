<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Result</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: background-color 0.5s ease;
        }

        .result-container {
            text-align: center;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            background: white;
            max-width: 500px;
            width: 90%;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 0.6s ease-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .result-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: white;
        }

        .result-message {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.9);
        }

        .student-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .student-name {
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
            margin-bottom: 10px;
        }

        .student-details {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
        }

        .action-buttons {
            margin-top: 30px;
        }

        .btn-action {
            padding: 12px 30px;
            border: 2px solid white;
            background: transparent;
            color: white;
            border-radius: 25px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn-action:hover {
            background: white;
            color: #333;
            transform: translateY(-2px);
        }

        .btn-primary-action {
            background: white;
            color: #333;
        }

        .btn-primary-action:hover {
            background: transparent;
            color: white;
        }

        /* Background colors based on status */
        .bg-success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .bg-error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .bg-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .auto-redirect {
            margin-top: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .countdown {
            font-weight: 600;
            color: white;
        }

        @media (max-width: 768px) {
            .result-container {
                padding: 30px 20px;
            }
            
            .result-icon {
                font-size: 3rem;
            }
            
            .result-title {
                font-size: 1.5rem;
            }
            
            .result-message {
                font-size: 1rem;
            }
            
            .btn-action {
                padding: 10px 20px;
                margin: 5px;
                display: block;
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body class="bg-{{ background_color }}">
    <div class="result-container">
        <!-- Status Icon -->
        <div class="result-icon">
            {% if status == 'success' %}
                <i class="fas fa-check-circle text-white"></i>
            {% elif status == 'error' %}
                <i class="fas fa-times-circle text-white"></i>
            {% elif status == 'warning' %}
                <i class="fas fa-exclamation-triangle text-white"></i>
            {% else %}
                <i class="fas fa-info-circle text-white"></i>
            {% endif %}
        </div>

        <!-- Result Title -->
        <h1 class="result-title">
            {% if status == 'success' %}
                Attendance Marked!
            {% elif status == 'expired' %}
                QR Code Expired
            {% elif status == 'error' %}
                Scan Failed
            {% else %}
                Attendance Status
            {% endif %}
        </h1>

        <!-- Result Message -->
        <p class="result-message">{{ message }}</p>

        <!-- Student Information -->
        {% if student %}
        <div class="student-info">
            <div class="student-name">
                <i class="fas fa-user me-2"></i>{{ student.name }}
            </div>
            <div class="student-details">
                <div><strong>ID:</strong> {{ student.unique_id }}</div>
                <div><strong>Email:</strong> {{ student.email }}</div>
                {% if attendance %}
                <div><strong>Time:</strong> {{ attendance.scan_datetime|date:"H:i:s" }}</div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{% url 'attendance:scanner_page' %}" class="btn-action btn-primary-action">
                <i class="fas fa-qrcode me-2"></i>Scan Another
            </a>
            <a href="{% url 'attendance:today_attendance' %}" class="btn-action">
                <i class="fas fa-list me-2"></i>View Today's List
            </a>
        </div>

        <!-- Auto-redirect countdown -->
        <div class="auto-redirect">
            Redirecting to scanner in <span class="countdown" id="countdown">5</span> seconds
        </div>
    </div>

    <!-- Audio elements -->
    {% if status == 'success' %}
    <audio id="resultAudio" preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    {% elif status == 'error' or status == 'expired' %}
    <audio id="resultAudio" preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    {% endif %}

    <script>
        // Enhanced audio feedback with fallback
        document.addEventListener('DOMContentLoaded', function() {
            const audio = document.getElementById('resultAudio');
            if (audio) {
                audio.play().catch(e => {
                    console.log('Audio play failed:', e);
                    // Fallback: create beep sound based on status
                    try {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const gainNode = audioContext.createGain();

                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);

                        // Different frequencies for different statuses
                        const status = '{{ status }}';
                        if (status === 'success') {
                            oscillator.frequency.value = 800; // High pitch for success
                        } else if (status === 'warning') {
                            oscillator.frequency.value = 600; // Medium pitch for warning
                        } else {
                            oscillator.frequency.value = 400; // Low pitch for error
                        }

                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.5);
                    } catch (fallbackError) {
                        console.log('Fallback audio also failed:', fallbackError);
                    }
                });
            }
        });

        // Auto-redirect countdown
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '{% url "attendance:scanner_page" %}';
            }
        }, 1000);

        // Allow user to cancel auto-redirect by clicking anywhere
        document.addEventListener('click', function() {
            clearInterval(timer);
            countdownElement.parentElement.style.display = 'none';
        });
    </script>
</body>
</html>
