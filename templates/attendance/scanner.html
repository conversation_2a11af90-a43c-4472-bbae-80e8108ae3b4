{% extends "base.html" %}
{% load static %}

{% block page_title %}Attendance Scanner{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'attendance:today_attendance' %}">Attendance</a></li>
<li class="breadcrumb-item active" aria-current="page">Scanner</li>
{% endblock %}

{% block content %}
<style>
    .scanner-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: white;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        box-sizing: border-box;
    }

    .scanner-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .scanner-header h1 {
        color: #2563eb;
        font-size: 2rem;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .scanner-header p {
        color: #6b7280;
        font-size: 1.1rem;
        margin: 0;
    }

    .scanner-box {
        width: 90vw;
        max-width: 500px;
        height: 90vw;
        max-height: 500px;
        border: 3px solid #2563eb;
        border-radius: 20px;
        overflow: hidden;
        position: relative;
        background: #f8fafc;
        box-shadow: 0 10px 30px rgba(37, 99, 235, 0.2);
    }

    #qr-reader {
        width: 100%;
        height: 100%;
    }

    .scanner-controls {
        margin-top: 30px;
        text-align: center;
    }

    .btn-scanner {
        background: #2563eb;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 10px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        margin: 0 10px;
        transition: all 0.3s ease;
    }

    .btn-scanner:hover {
        background: #1d4ed8;
        transform: translateY(-2px);
    }

    .btn-scanner:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
    }

    .scanner-status {
        margin-top: 20px;
        padding: 15px;
        border-radius: 10px;
        text-align: center;
        font-weight: 500;
        display: none;
    }

    .status-success {
        background: #dcfce7;
        color: #166534;
        border: 1px solid #bbf7d0;
    }

    .status-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .status-warning {
        background: #fffbeb;
        color: #d97706;
        border: 1px solid #fed7aa;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        z-index: 10;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #2563eb;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 15px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .close-scanner {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.5);
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        font-size: 1.2rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.3s ease;
    }

    .close-scanner:hover {
        background: rgba(0, 0, 0, 0.7);
    }

    @media (max-width: 768px) {
        .scanner-header h1 {
            font-size: 1.5rem;
        }
        
        .scanner-header p {
            font-size: 1rem;
        }
        
        .btn-scanner {
            padding: 10px 20px;
            font-size: 0.9rem;
        }
    }
</style>

<div class="scanner-container" id="scannerContainer">
    <button class="close-scanner" onclick="closeScanner()">
        <i class="fas fa-times"></i>
    </button>

    <div class="scanner-header">
        <h1><i class="fas fa-qrcode me-2"></i>Attendance Scanner</h1>
        <p>Position the QR code within the scanner frame</p>
    </div>

    <div class="scanner-box">
        <div id="qr-reader"></div>
        <div class="loading-overlay" id="loadingOverlay">
            <div class="spinner"></div>
            <p>Initializing camera...</p>
        </div>
    </div>

    <div class="scanner-controls">
        <button class="btn-scanner" id="startBtn" onclick="startScanner()">
            <i class="fas fa-play me-2"></i>Start Scanner
        </button>
        <button class="btn-scanner" id="stopBtn" onclick="stopScanner()" style="display: none;">
            <i class="fas fa-stop me-2"></i>Stop Scanner
        </button>
    </div>

    <div class="scanner-status" id="scannerStatus"></div>
</div>

<!-- Hidden form for submitting scan results -->
<form id="scanForm" method="POST" action="{% url 'attendance:scanner_result' %}" style="display: none;">
    {% csrf_token %}
    <input type="hidden" name="qr_content" id="qrContent">
</form>

<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script>
let html5QrCode;
let isScanning = false;

// Audio feedback
const successAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
const errorAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');

function initializeScanner() {
    html5QrCode = new Html5Qrcode("qr-reader");
    document.getElementById('loadingOverlay').style.display = 'none';
}

function startScanner() {
    if (isScanning) return;
    
    const config = {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0
    };
    
    html5QrCode.start(
        { facingMode: "environment" },
        config,
        onScanSuccess,
        onScanFailure
    ).then(() => {
        isScanning = true;
        document.getElementById('startBtn').style.display = 'none';
        document.getElementById('stopBtn').style.display = 'inline-block';
        showStatus('Scanner started. Point camera at QR code.', 'success');
    }).catch(err => {
        console.error('Failed to start scanner:', err);
        showStatus('Failed to start camera. Please check permissions.', 'error');
    });
}

function stopScanner() {
    if (!isScanning) return;
    
    html5QrCode.stop().then(() => {
        isScanning = false;
        document.getElementById('startBtn').style.display = 'inline-block';
        document.getElementById('stopBtn').style.display = 'none';
        showStatus('Scanner stopped.', 'warning');
    }).catch(err => {
        console.error('Failed to stop scanner:', err);
    });
}

function onScanSuccess(decodedText, decodedResult) {
    // Stop scanner immediately
    stopScanner();
    
    // Play success sound
    {% if settings.success_sound_enabled %}
    successAudio.play().catch(e => console.log('Audio play failed:', e));
    {% endif %}
    
    // Submit the form
    document.getElementById('qrContent').value = decodedText;
    document.getElementById('scanForm').submit();
}

function onScanFailure(error) {
    // This is called continuously while scanning, so we don't want to log every failure
    // console.warn('QR scan failed:', error);
}

function showStatus(message, type) {
    const statusDiv = document.getElementById('scannerStatus');
    statusDiv.textContent = message;
    statusDiv.className = `scanner-status status-${type}`;
    statusDiv.style.display = 'block';
    
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 3000);
}

function closeScanner() {
    if (isScanning) {
        stopScanner();
    }
    window.location.href = '{% url "attendance:today_attendance" %}';
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeScanner();
    
    // Auto-start scanner
    setTimeout(() => {
        startScanner();
    }, 1000);
});

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (document.hidden && isScanning) {
        stopScanner();
    }
});
</script>
{% endblock %}
