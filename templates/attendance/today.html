{% extends "base.html" %}
{% load static %}

{% block page_title %}Today's Attendance{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'attendance:today_attendance' %}">Attendance</a></li>
<li class="breadcrumb-item active" aria-current="page">Today</li>
{% endblock %}

{% block content %}
<style>
    .attendance-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px;
        color: white;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-card.expandable:hover {
        border-color: #667eea;
    }

    .stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .stat-label {
        font-size: 1.1rem;
        color: #6b7280;
        font-weight: 500;
    }

    .present-card { border-left: 5px solid #10b981; }
    .present-card .stat-icon { color: #10b981; }
    .present-card .stat-number { color: #10b981; }

    .expired-card { border-left: 5px solid #f59e0b; }
    .expired-card .stat-icon { color: #f59e0b; }
    .expired-card .stat-number { color: #f59e0b; }

    .invalid-card { border-left: 5px solid #ef4444; }
    .invalid-card .stat-icon { color: #ef4444; }
    .invalid-card .stat-number { color: #ef4444; }

    .total-card { border-left: 5px solid #6366f1; }
    .total-card .stat-icon { color: #6366f1; }
    .total-card .stat-number { color: #6366f1; }

    .students-list {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-top: 20px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        display: none;
    }

    .students-list.show {
        display: block;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .student-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #e5e7eb;
        transition: background 0.2s ease;
    }

    .student-item:hover {
        background: #f9fafb;
    }

    .student-item:last-child {
        border-bottom: none;
    }

    .student-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.2rem;
        margin-right: 15px;
    }

    .student-info {
        flex: 1;
    }

    .student-name {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 5px;
    }

    .student-details {
        color: #6b7280;
        font-size: 0.9rem;
    }

    .scan-time {
        color: #6366f1;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .quick-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
    }

    .action-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 10px;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #d1d5db;
    }

    @media (max-width: 768px) {
        .attendance-card {
            padding: 20px;
        }
        
        .stat-card {
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .stat-icon {
            font-size: 2.5rem;
        }
        
        .stat-number {
            font-size: 2rem;
        }
        
        .quick-actions {
            flex-direction: column;
        }
        
        .action-btn {
            justify-content: center;
        }
    }
</style>

<div class="container-fluid">
    <!-- Header Card -->
    <div class="attendance-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="fas fa-calendar-day me-3"></i>
                    Today's Attendance
                </h1>
                <p class="mb-0 opacity-90">
                    <i class="fas fa-clock me-2"></i>
                    {{ today|date:"l, F d, Y" }}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex flex-column align-items-md-end">
                    <div class="h4 mb-2">Total Scans</div>
                    <div class="display-4 fw-bold">{{ stats.total_scans }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="{% url 'attendance:scanner_page' %}" class="action-btn">
            <i class="fas fa-qrcode"></i>
            Scan QR Code
        </a>
        <a href="{% url 'attendance:attendance_log' %}" class="action-btn">
            <i class="fas fa-history"></i>
            View Logs
        </a>
        <a href="{% url 'attendance:export_attendance' %}?range=1" class="action-btn">
            <i class="fas fa-download"></i>
            Export Today
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card present-card expandable" onclick="toggleStudentsList('present')">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">{{ stats.total_present }}</div>
                <div class="stat-label">Present Students</div>
                <small class="text-muted mt-2 d-block">Click to view list</small>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card expired-card expandable" onclick="toggleStudentsList('expired')">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number">{{ stats.total_expired }}</div>
                <div class="stat-label">Expired QR Codes</div>
                <small class="text-muted mt-2 d-block">Click to view list</small>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card invalid-card expandable" onclick="toggleStudentsList('invalid')">
                <div class="stat-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number">{{ stats.total_invalid }}</div>
                <div class="stat-label">Invalid Scans</div>
                <small class="text-muted mt-2 d-block">Click to view list</small>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card total-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number">{{ stats.total_scans }}</div>
                <div class="stat-label">Total Scans</div>
            </div>
        </div>
    </div>

    <!-- Students Lists (Hidden by default) -->
    <div id="present-list" class="students-list">
        <h4 class="mb-4">
            <i class="fas fa-check-circle text-success me-2"></i>
            Present Students ({{ stats.total_present }})
        </h4>
        <div id="present-students">
            {% for record in present_students %}
            <div class="student-item">
                <div class="student-avatar">
                    {{ record.student.name|first|upper }}
                </div>
                <div class="student-info">
                    <div class="student-name">{{ record.student.name }}</div>
                    <div class="student-details">
                        ID: {{ record.student.unique_id }} | {{ record.student.email }}
                    </div>
                </div>
                <div class="scan-time">
                    {{ record.scan_datetime|date:"H:i:s" }}
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-user-check"></i>
                <p>No students marked present today</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <div id="expired-list" class="students-list">
        <h4 class="mb-4">
            <i class="fas fa-clock text-warning me-2"></i>
            Expired QR Codes ({{ stats.total_expired }})
        </h4>
        <div id="expired-students">
            {% for record in expired_students %}
            <div class="student-item">
                <div class="student-avatar">
                    {{ record.student.name|first|upper }}
                </div>
                <div class="student-info">
                    <div class="student-name">{{ record.student.name }}</div>
                    <div class="student-details">
                        ID: {{ record.student.unique_id }} | {{ record.student.email }}
                    </div>
                </div>
                <div class="scan-time">
                    {{ record.scan_datetime|date:"H:i:s" }}
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-clock"></i>
                <p>No expired QR code scans today</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <div id="invalid-list" class="students-list">
        <h4 class="mb-4">
            <i class="fas fa-times-circle text-danger me-2"></i>
            Invalid Scans ({{ stats.total_invalid }})
        </h4>
        <div id="invalid-students">
            {% for record in invalid_students %}
            <div class="student-item">
                <div class="student-avatar">
                    {% if record.student %}
                        {{ record.student.name|first|upper }}
                    {% else %}
                        ?
                    {% endif %}
                </div>
                <div class="student-info">
                    <div class="student-name">
                        {% if record.student %}
                            {{ record.student.name }}
                        {% else %}
                            Unknown Student
                        {% endif %}
                    </div>
                    <div class="student-details">
                        {% if record.student %}
                            ID: {{ record.student.unique_id }} | {{ record.student.email }}
                        {% else %}
                            Invalid QR Code
                        {% endif %}
                    </div>
                </div>
                <div class="scan-time">
                    {{ record.scan_datetime|date:"H:i:s" }}
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-shield-alt"></i>
                <p>No invalid scans today</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
function toggleStudentsList(status) {
    const listId = status + '-list';
    const listElement = document.getElementById(listId);
    
    // Hide all other lists
    const allLists = document.querySelectorAll('.students-list');
    allLists.forEach(list => {
        if (list.id !== listId) {
            list.classList.remove('show');
        }
    });
    
    // Toggle current list
    listElement.classList.toggle('show');
    
    // Scroll to list if showing
    if (listElement.classList.contains('show')) {
        setTimeout(() => {
            listElement.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }, 100);
    }
}

// Auto-refresh every 30 seconds
setInterval(() => {
    location.reload();
}, 30000);
</script>
{% endblock %}
