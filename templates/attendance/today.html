{% extends "base.html" %}
{% load static %}

{% block page_title %}Today's Attendance{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'attendance:today_attendance' %}">Attendance</a></li>
<li class="breadcrumb-item active" aria-current="page">Today</li>
{% endblock %}

{% block content %}
<style>
    .attendance-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px;
        color: white;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .live-attendance-card {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: var(--glass-shadow);
        color: var(--text-primary);
        transition: all 0.3s ease;
    }

    .live-indicator {
        animation: pulse 2s infinite;
        filter: drop-shadow(0 0 5px #10b981);
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.6; }
    }

    .live-count-container {
        text-align: center;
    }

    .live-count {
        font-size: 3.5rem;
        font-weight: 700;
        color: #10b981;
        line-height: 1;
        text-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
    }

    .live-label {
        font-size: 0.9rem;
        color: var(--text-secondary);
        font-weight: 500;
        margin-top: 5px;
    }

    .live-actions {
        border-top: 1px solid var(--border-color);
        padding-top: 20px;
    }

    .live-actions .btn {
        margin-right: 10px;
        border-radius: 10px;
        padding: 8px 16px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .live-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Live Students Modal */
    .live-students-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
    }

    .modal-content {
        position: relative;
        background: var(--bg-primary);
        border-radius: 20px;
        width: 90%;
        max-width: 800px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        padding: 20px 30px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: var(--glass-bg);
    }

    .modal-header h3 {
        margin: 0;
        color: var(--text-primary);
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .close-btn:hover {
        background: var(--glass-bg);
        color: var(--text-primary);
    }

    .modal-body {
        padding: 20px 30px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .live-student-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border: 1px solid var(--border-color);
        border-radius: 10px;
        margin-bottom: 10px;
        background: var(--glass-bg);
        transition: all 0.3s ease;
    }

    .live-student-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .student-info .student-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 5px;
    }

    .student-info .student-details {
        font-size: 0.85rem;
        color: var(--text-secondary);
    }

    .student-actions {
        display: flex;
        gap: 10px;
    }

    .student-actions .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        border-radius: 6px;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-card.expandable:hover {
        border-color: #667eea;
    }

    .stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .stat-label {
        font-size: 1.1rem;
        color: #6b7280;
        font-weight: 500;
    }

    .present-card { border-left: 5px solid #10b981; }
    .present-card .stat-icon { color: #10b981; }
    .present-card .stat-number { color: #10b981; }

    .expired-card { border-left: 5px solid #f59e0b; }
    .expired-card .stat-icon { color: #f59e0b; }
    .expired-card .stat-number { color: #f59e0b; }

    .invalid-card { border-left: 5px solid #ef4444; }
    .invalid-card .stat-icon { color: #ef4444; }
    .invalid-card .stat-number { color: #ef4444; }

    .total-card { border-left: 5px solid #6366f1; }
    .total-card .stat-icon { color: #6366f1; }
    .total-card .stat-number { color: #6366f1; }

    .students-list {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-top: 20px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        display: none;
    }

    .students-list.show {
        display: block;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .student-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #e5e7eb;
        transition: background 0.2s ease;
    }

    .student-item:hover {
        background: #f9fafb;
    }

    .student-item:last-child {
        border-bottom: none;
    }

    .student-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.2rem;
        margin-right: 15px;
    }

    .student-info {
        flex: 1;
    }

    .student-name {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 5px;
    }

    .student-details {
        color: #6b7280;
        font-size: 0.9rem;
    }

    .scan-time {
        color: #6366f1;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .quick-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
    }

    .action-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 10px;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #6b7280;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #d1d5db;
    }

    @media (max-width: 768px) {
        .attendance-card {
            padding: 20px;
        }
        
        .stat-card {
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .stat-icon {
            font-size: 2.5rem;
        }
        
        .stat-number {
            font-size: 2rem;
        }
        
        .quick-actions {
            flex-direction: column;
        }
        
        .action-btn {
            justify-content: center;
        }
    }
</style>

<div class="container-fluid">
    <!-- Header Card -->
    <div class="attendance-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="fas fa-calendar-day me-3"></i>
                    Today's Attendance
                </h1>
                <p class="mb-0 opacity-90">
                    <i class="fas fa-clock me-2"></i>
                    {{ today|date:"l, F d, Y" }}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex flex-column align-items-md-end">
                    <div class="h4 mb-2">Total Scans</div>
                    <div class="display-4 fw-bold">{{ stats.total_scans }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Attendance Card -->
    {% if role != 'scanner_only' %}
    <div class="live-attendance-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h3 class="mb-2">
                    <i class="fas fa-circle text-success me-2 live-indicator"></i>
                    Live Right Now
                </h3>
                <p class="mb-0 opacity-90">
                    Students currently present in the library
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="live-count-container">
                    <div class="live-count" id="liveCount">{{ stats.live_count }}</div>
                    <div class="live-label">Present Now</div>
                </div>
            </div>
        </div>

        {% if stats.live_count > 0 %}
        <div class="live-actions mt-3">
            <button class="btn btn-outline-primary" onclick="showLiveStudents()">
                <i class="fas fa-users me-2"></i>
                View Present Students
            </button>
            <button class="btn btn-outline-success" onclick="refreshLiveCount()">
                <i class="fas fa-sync-alt me-2"></i>
                Refresh
            </button>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="{% url 'attendance:scanner_page' %}" class="action-btn">
            <i class="fas fa-qrcode"></i>
            Scan QR Code
        </a>
        {% if role != 'scanner_only' %}
        <a href="{% url 'attendance:attendance_log' %}" class="action-btn">
            <i class="fas fa-history"></i>
            View Logs
        </a>
        <a href="{% url 'attendance:export_attendance' %}?range=1" class="action-btn">
            <i class="fas fa-download"></i>
            Export Today
        </a>
        {% endif %}
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card present-card expandable" onclick="toggleStudentsList('present')">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number">{{ stats.total_present }}</div>
                <div class="stat-label">Present Students</div>
                <small class="text-muted mt-2 d-block">Click to view list</small>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card expired-card expandable" onclick="toggleStudentsList('expired')">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number">{{ stats.total_expired }}</div>
                <div class="stat-label">Expired QR Codes</div>
                <small class="text-muted mt-2 d-block">Click to view list</small>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card invalid-card expandable" onclick="toggleStudentsList('invalid')">
                <div class="stat-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-number">{{ stats.total_invalid }}</div>
                <div class="stat-label">Invalid Scans</div>
                <small class="text-muted mt-2 d-block">Click to view list</small>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card total-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number">{{ stats.total_scans }}</div>
                <div class="stat-label">Total Scans</div>
            </div>
        </div>
    </div>

    <!-- Students Lists (Hidden by default) -->
    <div id="present-list" class="students-list">
        <h4 class="mb-4">
            <i class="fas fa-check-circle text-success me-2"></i>
            Present Students ({{ stats.total_present }})
        </h4>
        <div id="present-students">
            {% for record in present_students %}
            <div class="student-item">
                <div class="student-avatar">
                    {{ record.student.name|first|upper }}
                </div>
                <div class="student-info">
                    <div class="student-name">{{ record.student.name }}</div>
                    <div class="student-details">
                        ID: {{ record.student.unique_id }} | {{ record.student.email }}
                    </div>
                </div>
                <div class="scan-time">
                    {{ record.scan_datetime|date:"H:i:s" }}
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-user-check"></i>
                <p>No students marked present today</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <div id="expired-list" class="students-list">
        <h4 class="mb-4">
            <i class="fas fa-clock text-warning me-2"></i>
            Expired QR Codes ({{ stats.total_expired }})
        </h4>
        <div id="expired-students">
            {% for record in expired_students %}
            <div class="student-item">
                <div class="student-avatar">
                    {{ record.student.name|first|upper }}
                </div>
                <div class="student-info">
                    <div class="student-name">{{ record.student.name }}</div>
                    <div class="student-details">
                        ID: {{ record.student.unique_id }} | {{ record.student.email }}
                    </div>
                </div>
                <div class="scan-time">
                    {{ record.scan_datetime|date:"H:i:s" }}
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-clock"></i>
                <p>No expired QR code scans today</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <div id="invalid-list" class="students-list">
        <h4 class="mb-4">
            <i class="fas fa-times-circle text-danger me-2"></i>
            Invalid Scans ({{ stats.total_invalid }})
        </h4>
        <div id="invalid-students">
            {% for record in invalid_students %}
            <div class="student-item">
                <div class="student-avatar">
                    {% if record.student %}
                        {{ record.student.name|first|upper }}
                    {% else %}
                        ?
                    {% endif %}
                </div>
                <div class="student-info">
                    <div class="student-name">
                        {% if record.student %}
                            {{ record.student.name }}
                        {% else %}
                            Unknown Student
                        {% endif %}
                    </div>
                    <div class="student-details">
                        {% if record.student %}
                            ID: {{ record.student.unique_id }} | {{ record.student.email }}
                        {% else %}
                            Invalid QR Code
                        {% endif %}
                    </div>
                </div>
                <div class="scan-time">
                    {{ record.scan_datetime|date:"H:i:s" }}
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-shield-alt"></i>
                <p>No invalid scans today</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
function toggleStudentsList(status) {
    const listId = status + '-list';
    const listElement = document.getElementById(listId);
    
    // Hide all other lists
    const allLists = document.querySelectorAll('.students-list');
    allLists.forEach(list => {
        if (list.id !== listId) {
            list.classList.remove('show');
        }
    });
    
    // Toggle current list
    listElement.classList.toggle('show');
    
    // Scroll to list if showing
    if (listElement.classList.contains('show')) {
        setTimeout(() => {
            listElement.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }, 100);
    }
}

// Live attendance functionality
function showLiveStudents() {
    fetch('/attendance/api/live-students/')
        .then(response => response.json())
        .then(data => {
            if (data.students && data.students.length > 0) {
                showLiveStudentsModal(data.students);
            } else {
                alert('No students currently present');
            }
        })
        .catch(error => {
            console.error('Error fetching live students:', error);
            alert('Error loading live students');
        });
}

function showLiveStudentsModal(students) {
    const modal = document.createElement('div');
    modal.className = 'live-students-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeLiveModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-users me-2"></i>Currently Present Students</h3>
                <button class="close-btn" onclick="closeLiveModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${students.map(student => `
                    <div class="live-student-item">
                        <div class="student-info">
                            <div class="student-name">${student.student_name}</div>
                            <div class="student-details">
                                ID: ${student.student_unique_id} |
                                Check-in: ${student.check_in_time} |
                                Duration: ${student.duration}
                            </div>
                        </div>
                        <div class="student-actions">
                            <button class="btn btn-sm btn-warning" onclick="checkoutStudent(${student.id}, '${student.student_name}')">
                                <i class="fas fa-sign-out-alt me-1"></i>Logout Now
                            </button>
                            <button class="btn btn-sm btn-info" onclick="showBackdateModal(${student.id}, '${student.student_name}', '${student.check_in_time}')">
                                <i class="fas fa-clock me-1"></i>Backdate Logout
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function closeLiveModal() {
    const modal = document.querySelector('.live-students-modal');
    if (modal) {
        modal.remove();
    }
}

function checkoutStudent(attendanceId, studentName) {
    const reason = prompt(`Select reason for ${studentName}'s logout:\n1. Forgot to Scan Out\n2. Left Early (Emergency)\n3. System/Scanner Error\n4. Manual Correction\n\nEnter number (1-4):`);

    const reasonMap = {
        '1': 'forgot_scan',
        '2': 'left_early',
        '3': 'system_error',
        '4': 'manual_correction'
    };

    if (!reasonMap[reason]) {
        alert('Invalid reason selected');
        return;
    }

    if (confirm(`Confirm logout for ${studentName}?`)) {
        fetch('/attendance/api/checkout/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                attendance_id: attendanceId,
                reason: reasonMap[reason]
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                closeLiveModal();
                refreshLiveCount();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error processing checkout');
        });
    }
}

function refreshLiveCount() {
    fetch('/attendance/api/today-stats/')
        .then(response => response.json())
        .then(data => {
            document.getElementById('liveCount').textContent = data.live_count || 0;
        })
        .catch(error => {
            console.error('Error refreshing live count:', error);
        });
}

// Auto-refresh live count every 30 seconds
setInterval(() => {
    refreshLiveCount();
}, 30000);
</script>
{% endblock %}
