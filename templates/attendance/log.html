{% extends "base.html" %}
{% load static %}

{% block page_title %}Attendance Log{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'attendance:today_attendance' %}">Attendance</a></li>
<li class="breadcrumb-item active" aria-current="page">Log</li>
{% endblock %}

{% block content %}
<style>
    .log-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px;
        color: white;
        margin-bottom: 30px;
    }

    .filter-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-box {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #667eea;
    }

    .stat-box.success { border-left-color: #10b981; }
    .stat-box.warning { border-left-color: #f59e0b; }
    .stat-box.danger { border-left-color: #ef4444; }
    .stat-box.info { border-left-color: #6366f1; }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #6b7280;
        font-weight: 500;
    }

    .records-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .table-header {
        background: #f8fafc;
        padding: 20px;
        border-bottom: 1px solid #e5e7eb;
    }

    .table th {
        background: #f8fafc;
        border: none;
        font-weight: 600;
        color: #374151;
        padding: 15px;
    }

    .table td {
        border: none;
        padding: 15px;
        vertical-align: middle;
    }

    .table tbody tr {
        border-bottom: 1px solid #f3f4f6;
        transition: background 0.2s ease;
    }

    .table tbody tr:hover {
        background: #f9fafb;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-present {
        background: #dcfce7;
        color: #166534;
    }

    .status-expired {
        background: #fef3c7;
        color: #92400e;
    }

    .status-invalid {
        background: #fecaca;
        color: #991b1b;
    }

    .filter-form {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }

    .form-group {
        flex: 1;
        min-width: 150px;
    }

    .form-label {
        font-weight: 500;
        color: #374151;
        margin-bottom: 8px;
    }

    .form-control {
        border: 2px solid #e5e7eb;
        border-radius: 10px;
        padding: 10px 15px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn-filter {
        background: #667eea;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-filter:hover {
        background: #5a67d8;
        transform: translateY(-2px);
    }

    .btn-export {
        background: #10b981;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 10px;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        background: #059669;
        color: white;
        transform: translateY(-2px);
    }

    .pagination {
        justify-content: center;
        margin-top: 30px;
    }

    .page-link {
        border: none;
        color: #667eea;
        padding: 10px 15px;
        margin: 0 5px;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .page-link:hover {
        background: #667eea;
        color: white;
    }

    .page-item.active .page-link {
        background: #667eea;
        border-color: #667eea;
    }

    .insights-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .insight-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-left: 4px solid #667eea;
        background: #f8fafc;
        border-radius: 10px;
        margin-bottom: 15px;
    }

    .insight-icon {
        font-size: 2rem;
        color: #667eea;
        margin-right: 15px;
    }

    .insight-content h6 {
        margin: 0 0 5px 0;
        color: #1f2937;
        font-weight: 600;
    }

    .insight-content p {
        margin: 0;
        color: #6b7280;
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .filter-form {
            flex-direction: column;
        }
        
        .form-group {
            width: 100%;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .table-responsive {
            font-size: 0.9rem;
        }
    }
</style>

<div class="container-fluid">
    <!-- Header -->
    <div class="log-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="fas fa-history me-3"></i>
                    Attendance Log
                </h1>
                <p class="mb-0 opacity-90">
                    View and analyze attendance records from {{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'attendance:export_attendance' %}?range={{ date_range }}{% if date_range == 'custom' %}&start_date={{ start_date|date:'Y-m-d' }}&end_date={{ end_date|date:'Y-m-d' }}{% endif %}" class="btn-export">
                    <i class="fas fa-download"></i>
                    Export Data
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-card">
        <h5 class="mb-4">
            <i class="fas fa-filter me-2"></i>
            Filter Records
        </h5>
        <form method="GET" class="filter-form">
            <div class="form-group">
                <label class="form-label">Date Range</label>
                <select name="range" class="form-control" onchange="toggleCustomDates(this.value)">
                    <option value="30" {% if date_range == '30' %}selected{% endif %}>Last 30 Days</option>
                    <option value="90" {% if date_range == '90' %}selected{% endif %}>Last 3 Months</option>
                    <option value="180" {% if date_range == '180' %}selected{% endif %}>Last 6 Months</option>
                    <option value="365" {% if date_range == '365' %}selected{% endif %}>Last Year</option>
                    <option value="custom" {% if date_range == 'custom' %}selected{% endif %}>Custom Range</option>
                </select>
            </div>
            
            <div class="form-group" id="start-date-group" style="{% if date_range != 'custom' %}display: none;{% endif %}">
                <label class="form-label">Start Date</label>
                <input type="date" name="start_date" class="form-control" value="{{ start_date|date:'Y-m-d' }}">
            </div>
            
            <div class="form-group" id="end-date-group" style="{% if date_range != 'custom' %}display: none;{% endif %}">
                <label class="form-label">End Date</label>
                <input type="date" name="end_date" class="form-control" value="{{ end_date|date:'Y-m-d' }}">
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn-filter">
                    <i class="fas fa-search me-2"></i>
                    Apply Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-box success">
            <div class="stat-value">{{ stats.present_count }}</div>
            <div class="stat-label">Present Students</div>
        </div>
        <div class="stat-box warning">
            <div class="stat-value">{{ stats.expired_count }}</div>
            <div class="stat-label">Expired QR Codes</div>
        </div>
        <div class="stat-box danger">
            <div class="stat-value">{{ stats.invalid_count }}</div>
            <div class="stat-label">Invalid Scans</div>
        </div>
        <div class="stat-box info">
            <div class="stat-value">{{ stats.unique_students }}</div>
            <div class="stat-label">Unique Students</div>
        </div>
    </div>

    <!-- Insights -->
    {% if stats.most_frequent or stats.max_attendance_day or stats.overused_students %}
    <div class="insights-card">
        <h5 class="mb-4">
            <i class="fas fa-chart-line me-2"></i>
            Insights & Analytics
        </h5>
        
        {% if stats.most_frequent %}
        <div class="insight-item">
            <div class="insight-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="insight-content">
                <h6>Most Frequent Visitor</h6>
                <p>{{ stats.most_frequent.student__name }} ({{ stats.most_frequent.student__unique_id }}) with {{ stats.most_frequent.visit_count }} visits</p>
            </div>
        </div>
        {% endif %}
        
        {% if stats.max_attendance_day %}
        <div class="insight-item">
            <div class="insight-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="insight-content">
                <h6>Peak Attendance Day</h6>
                <p>{{ stats.max_attendance_day.scan_date|date:"F d, Y" }} with {{ stats.max_attendance_day.daily_count }} students</p>
            </div>
        </div>
        {% endif %}
        
        {% if stats.overused_students %}
        <div class="insight-item">
            <div class="insight-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="insight-content">
                <h6>High Usage Alert</h6>
                <p>{{ stats.overused_students.count }} student(s) with more than 20 visits in this period</p>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Records Table -->
    <div class="records-table">
        <div class="table-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Attendance Records ({{ page_obj.paginator.count }} total)
            </h5>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Date & Time</th>
                        <th>Student</th>
                        <th>Status</th>
                        <th>Scanned By</th>
                        <th>IP Address</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in page_obj %}
                    <tr>
                        <td>
                            <div class="fw-bold">{{ record.scan_date|date:"M d, Y" }}</div>
                            <small class="text-muted">{{ record.scan_datetime|date:"H:i:s" }}</small>
                        </td>
                        <td>
                            {% if record.student %}
                                <div class="fw-bold">{{ record.student.name }}</div>
                                <small class="text-muted">{{ record.student.unique_id }} | {{ record.student.email }}</small>
                            {% else %}
                                <span class="text-muted">Unknown Student</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge status-{{ record.status }}">
                                {{ record.status|title }}
                            </span>
                        </td>
                        <td>
                            <small>{{ record.scanner_name }}</small>
                        </td>
                        <td>
                            <small class="text-muted">{{ record.ip_address|default:"N/A" }}</small>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No attendance records found for the selected period.</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Attendance records pagination">
        <ul class="pagination">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if request.GET.range %}&range={{ request.GET.range }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}">First</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.range %}&range={{ request.GET.range }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}">Previous</a>
                </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
            </li>

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.range %}&range={{ request.GET.range }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}">Next</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.range %}&range={{ request.GET.range }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}">Last</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<script>
function toggleCustomDates(value) {
    const startDateGroup = document.getElementById('start-date-group');
    const endDateGroup = document.getElementById('end-date-group');
    
    if (value === 'custom') {
        startDateGroup.style.display = 'block';
        endDateGroup.style.display = 'block';
    } else {
        startDateGroup.style.display = 'none';
        endDateGroup.style.display = 'none';
    }
}
</script>
{% endblock %}
