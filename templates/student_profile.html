{% extends "base.html" %}

{% block title %}Student Profile - Librainian{% endblock %}

{% block extra_css %}
<style>
        /* Template-specific styles - CSS variables inherited from base.html */

        /* Glass Design Components */
        .glass-container {
            min-height: 100vh;
            padding: 2rem 1rem;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .glass-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            text-align: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .glass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            transform: rotate(-45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) rotate(-45deg); }
            50% { transform: translateX(100%) rotate(-45deg); }
        }

        .glass-header h3 {
            margin: 0;
            font-weight: 700;
            font-size: 1.75rem;
            position: relative;
            z-index: 1;
        }

        .glass-body {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* Profile Image */
        .profile-image {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid var(--primary);
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
            margin: 0 auto 2rem;
            display: block;
        }

        .profile-placeholder {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
        }

        .profile-placeholder i {
            font-size: 4rem;
            color: white;
        }

        /* Info Cards */
        .info-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .info-card-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
        }

        .info-card-success {
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            color: white;
        }

        .info-card-warning {
            background: linear-gradient(135deg, var(--warning), #d97706);
            color: white;
        }

        .info-card-info {
            background: linear-gradient(135deg, var(--info), #2563eb);
            color: white;
        }

        .info-card-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.9;
        }

        .info-card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .info-card-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0;
        }

        /* Student Details */
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .detail-value {
            font-weight: 500;
            color: var(--text-primary);
        }

        /* Action Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(99, 102, 241, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
            background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
            color: white;
            text-decoration: none;
        }










        /* Responsive Design */
        @media (max-width: 768px) {
            .glass-container {
                padding: 1rem 0.5rem;
            }

            .glass-card {
                border-radius: 20px;
            }

            .glass-body {
                padding: 1.5rem;
            }

            .glass-header {
                padding: 1.5rem;
            }

            .glass-header h3 {
                font-size: 1.5rem;
            }

            .profile-image,
            .profile-placeholder {
                width: 150px;
                height: 150px;
            }

            .profile-placeholder i {
                font-size: 3rem;
            }

            .info-card-icon {
                font-size: 2.5rem;
            }

            .info-card-value {
                font-size: 1.75rem;
            }

            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .glass-body {
                padding: 1rem;
            }

            .profile-image,
            .profile-placeholder {
                width: 120px;
                height: 120px;
            }

            .profile-placeholder i {
                font-size: 2.5rem;
            }

            .info-card {
                padding: 1rem;
            }

            .info-card-icon {
                font-size: 2rem;
            }

            .info-card-value {
                font-size: 1.5rem;
            }
        }

        /* Dark Mode Compatibility */
        body.dark-mode .glass-card {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            box-shadow: var(--glass-shadow);
        }

        body.dark-mode .glass-body {
            background: rgba(255, 255, 255, 0.03);
        }

        body.dark-mode .info-card {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        body.dark-mode .detail-row {
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        }

        body.dark-mode .table th {
            background: rgba(255, 255, 255, 0.08);
        }

        body.dark-mode .table td {
            background: rgba(255, 255, 255, 0.03);
        }

        body.dark-mode .table tbody tr:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        /* Recent Invoices Table Styles */
        .table {
            background: transparent;
            color: var(--text-primary);
            border: none;
        }

        .table th {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: none;
            font-weight: 600;
            padding: 1rem;
        }

        .table td {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .table a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
        }

        .table a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        /* Mobile responsive table */
        @media (max-width: 768px) {
            .table-responsive {
                border: none;
            }

            .table thead {
                display: none;
            }

            .table tbody tr {
                display: block;
                margin-bottom: 1rem;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 1rem;
            }

            .table tbody td {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: transparent;
                border: none;
                padding: 0.5rem 0;
            }

            .table tbody td:before {
                content: attr(data-label) ": ";
                font-weight: 600;
                color: var(--text-secondary);
                min-width: 120px;
            }
        }

        /* Pagination Styles */
        .pagination {
            margin: 0;
        }

        .pagination .page-link {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
            padding: 0.5rem 0.75rem;
            margin: 0 0.125rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .pagination .page-link:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: var(--primary);
            color: var(--primary);
            transform: translateY(-1px);
        }

        .pagination .page-item.active .page-link {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
        }



        /* Small Button Styles */
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary {
            border: 1px solid var(--primary);
            color: var(--primary);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-1px);
        }

        /* Dark mode pagination */
        body.dark-mode .pagination .page-link {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .pagination .page-link:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* Disable zoom on mobile */
        @media (max-width: 768px) {
            body {
                touch-action: pan-x pan-y;
            }
        }

    </style>
{% endblock %}

{% block content %}
<div class="student-profile-content fade-in">
    <div class="glass-container">
        <div class="container-fluid">
            <!-- Student Profile Content -->
            <div class="row">
                <!-- Profile Image and Quick Stats -->
                <div class="col-lg-4">
                    <div class="glass-card">
                        <div class="glass-body text-center">
                            {% if std.image %}
                                <img src="{{ std.image.url }}" alt="{{ std.name }}" class="profile-image">
                            {% else %}
                                <div class="profile-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            {% endif %}

                            <h4 class="mb-3">{{ std.name }}</h4>
                            <p class="text-muted mb-4">{{ std.course }}</p>

                            <!-- Quick Actions -->
                            <div class="d-grid gap-2">
                                <a href="/students/update/{{ std.slug }}" class="btn-primary">
                                    <i class="fas fa-edit"></i>
                                    Update Profile
                                </a>
                                {% if fee_status.is_paid == False %}
                                <a href="/students/create_registration/{{ std.slug }}" class="btn-secondary">
                                    <i class="fas fa-money-bill-wave"></i>
                                    Pay Registration Fee
                                </a>
                                {% endif %}
                                <a href="/students/create_invoice/{{ std.slug }}" class="btn-secondary">
                                    <i class="fas fa-plus-circle"></i>
                                    Create Invoice
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Invoices - Now Wider -->
                <div class="col-lg-8">
                    <div class="glass-card">
                        <div class="glass-header">
                            <h5><i class="fas fa-receipt me-2"></i>Recent Invoices</h5>
                        </div>
                        <div class="glass-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Invoice ID</th>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Payment Mode</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for inv in recent_invoices %}
                                        <tr>
                                            <td data-label="Invoice ID">
                                                <a href="/students/invoice_student/{{ inv.slug }}" class="text-decoration-none">
                                                    {{ inv.invoice_id }}
                                                </a>
                                            </td>
                                            <td data-label="Date">{{ inv.issue_date }}</td>
                                            <td data-label="Amount"><strong>₹{{ inv.total_amount }}</strong></td>
                                            <td data-label="Payment Mode">{{ inv.mode_pay|default:"N/A" }}</td>
                                            <td data-label="Actions">
                                                <a href="/students/invoice_student/{{ inv.slug }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">No invoices found</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            {% if recent_invoices.has_other_pages %}
                            <nav aria-label="Invoice pagination" class="mt-3">
                                <ul class="pagination pagination-sm justify-content-center">
                                    {% if recent_invoices.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ recent_invoices.previous_page_number }}">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in recent_invoices.paginator.page_range %}
                                        {% if recent_invoices.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > recent_invoices.number|add:'-3' and num < recent_invoices.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if recent_invoices.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ recent_invoices.next_page_number }}">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Student Details - Below Recent Invoices -->
                    <div class="glass-card mt-4">
                        <div class="glass-header">
                            <h5><i class="fas fa-user-edit me-2"></i>Student Details</h5>
                        </div>
                        <div class="glass-body">
                            <!-- Personal Information -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-id-card"></i>
                                            Registration ID
                                        </span>
                                        <span class="detail-value">{{ std.unique_id }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-user-check"></i>
                                            Status
                                        </span>
                                        <span class="detail-value">
                                            {% if std.is_active %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle me-1"></i>Active
                                                </span>
                                            {% else %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times-circle me-1"></i>Inactive
                                                </span>
                                            {% endif %}
                                        </span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-user"></i>
                                            Full Name
                                        </span>
                                        <span class="detail-value">{{ std.name }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-venus-mars"></i>
                                            Gender
                                        </span>
                                        <span class="detail-value">{{ std.gender }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-envelope"></i>
                                            Email
                                        </span>
                                        <span class="detail-value">{{ std.email }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-book"></i>
                                            Course
                                        </span>
                                        <span class="detail-value">{{ std.course }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-phone"></i>
                                            Mobile
                                        </span>
                                        <span class="detail-value">{{ std.mobile }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-calendar-plus"></i>
                                            Registration Date
                                        </span>
                                        <span class="detail-value">{{ std.registration_date }}</span>
                                    </div>
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="fas fa-rupee-sign"></i>
                                            Registration Fee
                                        </span>
                                        <span class="detail-value">₹{{ std.registration_fee }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information -->
                            <div class="detail-row">
                                <span class="detail-label">
                                    <i class="fas fa-map-marker-alt"></i>
                                    Address
                                </span>
                                <span class="detail-value">{{ std.locality }}, {{ std.city }}, {{ std.state }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript dependencies -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Modern functionality
        $(document).ready(function() {
            // Auto-hide alerts after 10 seconds
            $('.alert').each(function() {
                const alert = this;
                setTimeout(function() {
                    $(alert).fadeOut(1000);
                }, 10000);
            });

            // Loading state for action buttons
            $('.btn-primary, .btn-secondary').on('click', function() {
                const btn = $(this);
                const originalText = btn.html();
                btn.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
                btn.prop('disabled', true);

                // Re-enable after navigation (this won't execute if page changes)
                setTimeout(() => {
                    btn.html(originalText);
                    btn.prop('disabled', false);
                }, 2000);
            });

            // Smooth animations for cards
            $('.glass-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
            });
        });
    </script>
</div>
{% endblock %}
