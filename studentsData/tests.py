from django.test import TestCase
from django.contrib.auth.models import User
from django.utils import timezone
from librarian.models import Librarian_param
from manager.models import Manager_param
from libraryCommander.models import LibraryCommander_param
from .models import StudentData, States, Courses
import re


class StudentUniqueIDTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create library commander
        self.commander_user = User.objects.create_user(
            username='commander',
            first_name='Commander',
            last_name='Test'
        )
        self.commander = LibraryCommander_param.objects.create(
            user=self.commander_user,
            librarycommander_phone_num=1234567890,
            librarycommander_address='Test Address'
        )

        # Create manager
        self.manager_user = User.objects.create_user(
            username='manager',
            first_name='Manager',
            last_name='Test'
        )
        self.manager = Manager_param.objects.create(
            user=self.manager_user,
            library_commander=self.commander,
            manager_phone_num=1234567890,
            manager_address='Test Manager Address'
        )

        # Create librarian with first name "<PERSON>"
        self.librarian_user = User.objects.create_user(
            username='ramlibrarian',
            first_name='<PERSON>',
            last_name='<PERSON>'
        )
        self.librarian = Librarian_param.objects.create(
            user=self.librarian_user,
            manager=self.manager,
            library_name='Test Library',
            librarian_address='Test Address',
            librarian_phone_num=1234567890
        )

        # Create test state and course
        self.state = States.objects.create(name='Test State')
        self.course = Courses.objects.create(name='Test Course')

    def test_unique_id_generation_format(self):
        """Test that unique ID is generated in the correct format"""
        student = StudentData(
            librarian=self.librarian,
            course=self.course,
            name='Test Student',
            gender='Male',
            email='<EMAIL>',
            mobile=9876543210,
            locality='Test Locality',
            city='Test City',
            state=self.state,
            registration_fee=1000
        )

        # Generate the unique ID
        unique_id = student.generate_unique_id()

        # Test the format
        self.assertTrue(unique_id.startswith('reg'), "ID should start with 'reg'")
        self.assertEqual(unique_id[3:5], 'ra', "Should use first 2 letters of librarian's first name")

        # Test that it contains month/year
        now = timezone.now()
        expected_date = now.strftime("%m%y")
        self.assertEqual(unique_id[5:9], expected_date, f"Should contain current month/year {expected_date}")

        # Test length (should be around 21 characters)
        self.assertGreaterEqual(len(unique_id), 17, "ID should be at least 17 characters")
        self.assertLessEqual(len(unique_id), 25, "ID should not exceed 25 characters")

        print(f"Generated ID: {unique_id}")
        print(f"ID Length: {len(unique_id)}")

    def test_unique_id_auto_generation_on_save(self):
        """Test that unique ID is automatically generated when saving a student"""
        student = StudentData.objects.create(
            librarian=self.librarian,
            course=self.course,
            name='Auto Test Student',
            gender='Female',
            email='<EMAIL>',
            mobile=9876543211,
            locality='Auto Locality',
            city='Auto City',
            state=self.state,
            registration_fee=1500
        )

        # Check that unique_id was automatically generated
        self.assertIsNotNone(student.unique_id, "Unique ID should be automatically generated")
        self.assertTrue(student.unique_id.startswith('reg'), "Auto-generated ID should start with 'reg'")

        print(f"Auto-generated ID: {student.unique_id}")

    def test_unique_id_uniqueness(self):
        """Test that multiple students get unique IDs"""
        students = []
        unique_ids = set()

        for i in range(3):
            student = StudentData.objects.create(
                librarian=self.librarian,
                course=self.course,
                name=f'Student {i+1}',
                gender='Male' if i % 2 == 0 else 'Female',
                email=f'student{i+1}@example.com',
                mobile=9876543210 + i,
                locality=f'Locality {i+1}',
                city=f'City {i+1}',
                state=self.state,
                registration_fee=1000 + (i * 100)
            )
            students.append(student)
            unique_ids.add(student.unique_id)
            print(f"Student {i+1} ID: {student.unique_id}")

        # All IDs should be unique
        self.assertEqual(len(unique_ids), 3, "All generated IDs should be unique")
