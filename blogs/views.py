from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from .models import Blog
from libraryCommander.models import LibraryCommander_param
from rest_framework.views   import View
from django.db.models import Q
from django.core.paginator import Paginator
from django.core.cache import cache


@login_required(login_url="/librarycommander/login/")
def blog_list(request):
    try:
        user = request.user
        librarycommander = LibraryCommander_param.objects.get(user=user)
        if librarycommander.is_admin and user.is_authenticated:
            blogs = Blog.objects.all()
            return render(
                request, "blog_list.html", {"blogs": blogs, "role": "librarycommander"}
            )
        else:
            return render(request, "unauthorized.html", {"role": "librarycommander"})
    except LibraryCommander_param.DoesNotExist:
        return redirect("/librarycommander/login/")
    except Exception as e:
        return redirect("/librarycommander/login/")


# View a single blog
@login_required(login_url="/librarycommander/login/")
def blog_detail(request, slug):
    try:
        user = request.user
        librarycommander = LibraryCommander_param.objects.get(user=user)
        if librarycommander.is_admin and user.is_authenticated:

            blog = get_object_or_404(Blog, slug=slug)

            return render(
                request, "blog_detail.html", {"blog": blog, "role": "librarycommander"}
            )
        else:
            return redirect("/librarycommander/login/")
    except LibraryCommander_param.DoesNotExist:
        return redirect("/librarycommander/login/")
    except Exception as e:
        return redirect("/librarycommander/login/")


def blog_display(request, slug):
    try:
        blogs = Blog.objects.all()
        blog = get_object_or_404(Blog, slug=slug)
        return render(request, "blog_display.html", {"blog": blog, "blogs": blogs})
    except Exception as e:
        return redirect("/")


def blogs_display(request):
    try:
        # Check total blogs first
        total_blogs = Blog.objects.count()

        # Get published blogs
        blogs = Blog.objects.filter(status='published')

        # If no published blogs, show all blogs for testing
        if blogs.count() == 0 and total_blogs > 0:
            blogs = Blog.objects.all()

        # Get categories
        categories = blogs.values_list('category', flat=True).distinct() if blogs.exists() else []
        popular_tags = []  # You can implement tag logic later if needed

        context = {
            "blogs": blogs,
            "categories": categories,
            "popular_tags": popular_tags,
            "category": None,
            "tag": None
        }

        # Test if we can access the first blog's fields (debug prints removed)

        return render(request, "blogs_pages.html", context)
    except Exception as e:
        return redirect("/")


def cat_blogs_display(request, category):
    try:
        blog = Blog.objects.filter(category=category)
        return render(request, "blogs_pages.html", {"blogs": blog})
    except Exception as e:
        return redirect("/")


# @login_required(login_url="/librarycommander/login/")
# def blog_create(request):
#     try:
#         user = request.user
#         librarycommander = LibraryCommander_param.objects.get(user=user)

#         if librarycommander.is_admin and user.is_authenticated:
#             if request.method == "POST":
#                 title = request.POST.get("title")
#                 category = request.POST.get("category")
#                 keywords = request.POST.get("keywords")
#                 description = request.POST.get("description")
#                 content = request.POST.get("content")
#                 image = request.FILES.get("image")

#                 if title and category and description and content and image:

#                     Blog.objects.create(
#                         title=title,
#                         category=category,
#                         keyword=keywords,
#                         description=description,
#                         content=content,
#                         image=image,
#                         author=librarycommander,
#                     )
#                     messages.success(request, "Blog created successfully.")
#                     return redirect("/blogs/")
#                 else:
#                     messages.error(request, "All fields are required.")
#             else:
#                 blogs = Blog.objects.all()
#                 return render(
#                     request,
#                     "blog_form.html",
#                     {"role": "librarycommander", "blogs": blogs},
#                 )
#     except LibraryCommander_param.DoesNotExist:
#         return redirect("/librarycommander/login/")
#     except Exception as e:
#         return redirect("/librarycommander/login/")


# @login_required(login_url="/librarycommander/login/")
# def blog_update(request, slug):
#     try:
#         user = request.user
#         librarycommander = LibraryCommander_param.objects.get(user=user)
#         if librarycommander.is_admin and user.is_authenticated:
#             blog = get_object_or_404(Blog, slug=slug)

#             if request.method == "POST":
#                 title = request.POST.get("title")
#                 category = request.POST.get("category")
#                 keywords = request.POST.get("keywords")
#                 description = request.POST.get("description")
#                 content = request.POST.get("content")
#                 image = request.FILES.get("image")

#                 if title and category and description and content and keywords:
#                     blog.title = title
#                     blog.category = category
#                     blog.keyword = keywords
#                     blog.description = description
#                     blog.content = content
#                     if image:
#                         blog.image = image
#                     blog.save()
#                     messages.success(request, "Blog updated successfully.")
#                     return redirect("/blogs/")
#                 else:
#                     messages.error(request, "All fields are required.")
#                     return render(
#                         request,
#                         "blog_form_update.html",
#                         {"blog": blog, "role": "librarycommander"},
#                     )

#             else:
#                 return render(
#                     request,
#                     "blog_form_update.html",
#                     {"blog": blog, "role": "librarycommander"},
#                 )
#     except LibraryCommander_param.DoesNotExist:
#         return redirect("/librarycommander/login/")
#     except Exception as e:
#         return redirect("/librarycommander/login/")


@login_required(login_url="/librarycommander/login/")
def blog_create(request):
    try:
        user = request.user
        librarycommander = LibraryCommander_param.objects.get(user=user)

        if not librarycommander.is_admin or not user.is_authenticated:
            return redirect("/librarycommander/login/")

        if request.method == "POST":
            data = {
                "title": request.POST.get("title", None),
                "category": request.POST.get("category", None),
                "introduction": request.POST.get("introduction", None),
                "content": request.POST.get("content", None),
                "short_content": request.POST.get("short_content", None),
                "meta_keywords": request.POST.get("keywords", None),
                "meta_description": request.POST.get("meta_description", None),
                "image": request.FILES.get("image", None),
                "image_caption": request.POST.get("image_caption", None),
                # "internal_link": link,
                # "external_link": request.POST.get("external_link", None),
                "canonical_url": request.POST.get("canonical_url", None),
            }

            # Validate required fields
            required_fields = ["title", "category", "content", "image"]
            if not all(data.get(field) for field in required_fields):
                messages.error(request, "Please fill in all required fields.")
                return render(
                    request,
                    "blog_form.html",
                    {"role": "librarycommander", "data": data},
                )

            # Create blog post
            blog_post = Blog.objects.create(author=librarycommander, **data)

            messages.success(request, "Blog post created successfully.")
            return redirect("/blogs/")

        return render(request, "blog_form.html", {"role": "librarycommander"})

    except LibraryCommander_param.DoesNotExist:
        return redirect("/librarycommander/login/")
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")
        return redirect("/librarycommander/login/")


@login_required(login_url="/librarycommander/login/")
def blog_update(request, slug):
    try:
        user = request.user
        librarycommander = LibraryCommander_param.objects.get(user=user)

        if not librarycommander.is_admin or not user.is_authenticated:
            return redirect("/librarycommander/login/")

        blog_post = get_object_or_404(Blog, slug=slug)

        if request.method == "POST":
            # Update fields
            blog_post.title = request.POST.get("title", blog_post.title)
            blog_post.category = request.POST.get("category", blog_post.category)
            blog_post.introduction = request.POST.get(
                "introduction", blog_post.introduction
            )
            blog_post.content = request.POST.get("content", blog_post.content)
            blog_post.short_content = request.POST.get(
                "short_content", blog_post.short_content
            )
            blog_post.meta_keywords = request.POST.get(
                "meta_keywords", blog_post.meta_keywords
            )
            blog_post.meta_description = request.POST.get(
                "meta_description", blog_post.meta_description
            )
            blog_post.image_caption = request.POST.get(
                "image_caption", blog_post.image_caption
            )
            blog_post.internal_link = request.POST.get(
                "internal_link", blog_post.internal_link
            )
            blog_post.external_link = request.POST.get(
                "external_link", blog_post.external_link
            )
            blog_post.canonical_url = request.POST.get(
                "canonical_url", blog_post.canonical_url
            )

            if "image" in request.FILES:
                blog_post.image = request.FILES["image"]

            blog_post.save()
            messages.success(request, "Blog post updated successfully.")
            return redirect(blog_post.get_absolute_url())

        return render(
            request,
            "blog_form_update.html",
            {"blog": blog_post, "role": "librarycommander"},
        )

    except LibraryCommander_param.DoesNotExist:
        return redirect("/librarycommander/login/")
    except Exception as e:
        messages.error(request, f"An error occurred: {str(e)}")
        return redirect("/librarycommander/login/")


def blog_search(request):
    query = request.GET.get("q", "")
    category = request.GET.get("category", "")

    blogs = Blog.objects.filter(status="published")

    if query:
        blogs = blogs.filter(
            Q(title__icontains=query)
            | Q(content__icontains=query)
            | Q(meta_keywords__icontains=query)
            | Q(meta_description__icontains=query)
        )

    if category:
        blogs = blogs.filter(category=category)

    # Pagination
    paginator = Paginator(blogs, 10)
    page = request.GET.get("page")
    blogs = paginator.get_page(page)

    return render(
        request,
        "blog_search.html",
        {"blogs": blogs, "query": query, "category": category},
    )


@login_required(login_url="/librarycommander/login/")
def blog_delete(request, slug):
    try:
        user = request.user
        librarycommander = LibraryCommander_param.objects.get(user=user)
        if librarycommander.is_admin and user.is_authenticated:
            blog = get_object_or_404(Blog, slug=slug)

            blog.delete()
            messages.success(request, "Blog deleted successfully.")
            return redirect("/blogs/")
        else:
            return redirect("/librarycommander/login/")
    except LibraryCommander_param.DoesNotExist:
        return redirect("/librarycommander/login/")
    except Exception as e:
        return redirect("/librarycommander/login/")
from django.http import HttpResponse

def blog_myfunction(request):
    return HttpResponse("This is a test function")

from django.shortcuts import render, redirect, get_object_or_404
from membership.models import Coupon
import random
import string

def generate_coupon_code(length=8):
    # Define the characters to choose from
    characters = string.ascii_uppercase + string.digits  # A-Z and 0-9
   
    coupon_code = ''.join(random.choice(characters) for _ in range(length))
    
    return coupon_code


class CouponFormView(View):
    template_name = 'createCoupon.html'

    def get(self, request):
        coupons = Coupon.objects.all()
        return render(request, self.template_name, {'role':  "librarycommander"})
    
    def post(self, request):
        form ={
            "code" : request.POST.get("coupon_code"),#generate_coupon_code(),
            "discount_type" : request.POST.get("discount_type"),
            "discount" : request.POST.get("discount_value"),
            "expiry_date" : request.POST.get("expiryDate"),
            "usage_limit" : 1000.0 if request.POST.get("usageLimit") == "" else request.POST.get("usageLimit"),
            "overallDiscountAmount" : 1000.0 if request.POST.get("usageLimit") == "" else request.POST.get("usageLimit"),
        }
        try:
            form = Coupon(**form)
            form.save()
            messages.success(request, f"form sumbitted successfully")
            return redirect('/blogs/coupons/create_coupon/')

        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")
            return redirect('/blogs/coupons/create_coupon/')
            

class CouponView(View):
    template_name = 'couponList.html'
    
    def get(self, request):
        coupons = Coupon.objects.all().order_by('-created_at')
        return render(request, self.template_name, {'role':  "librarycommander", 'coupons': coupons})
        
    


# class CouponUpdateView(View):
#     template_name = 'coupon_form.html'

#     def get(self, request, pk):
#         coupon = get_object_or_404(Coupon, pk=pk)
#         form = CouponForm(instance=coupon)
#         return render(request, self.template_name, {'form': form})

#     def post(self, request, pk):
#         coupon = get_object_or_404(Coupon, pk=pk)
#         form = CouponForm(request.POST, instance=coupon)
#         if form.is_valid():
#             form.save()
#             return redirect('coupon_list')
#         return render(request, self.template_name, {'form': form})

class CouponDeleteView(View):

    def get(self, request, pk):
        try:
            coupon = get_object_or_404(Coupon, pk=pk)
            coupon.delete()
            messages.success(request, f"coupon deleted successfully")
        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")
            
        return redirect('/blogs/coupons/create_coupon/')
    

def coupon_detail_view(request, pk):
    if request.method == "POST":
        
        form ={
            "code" : request.POST.get("coupon_code"),#generate_coupon_code(),
            "discount_type" : request.POST.get("discount_type"),
            "discount" : request.POST.get("discount_value"),
            "expiry_date" : request.POST.get("expiryDate"),
            "usage_limit" : 1000.0 if request.POST.get("usageLimit") == "" else request.POST.get("usageLimit"),
            "overallDiscountAmount" : 1000.0 if request.POST.get("usageLimit") == "" else request.POST.get("usageLimit"),
        }
        try:
            coupon = get_object_or_404(Coupon, pk=pk)
            coupon.code = form['code']
            coupon.discount_type = form['discount_type']
            coupon.discount = form['discount']
            coupon.expiry_date = form['expiry_date']
            coupon.usage_limit = form['usage_limit']
            coupon.overallDiscountAmount = form['overallDiscountAmount']
            coupon.save()
            messages.success(request, f"form sumbitted successfully")
            return redirect('/blogs/coupons/create_coupon/')

        except Exception as e:
            messages.error(request, f"An error occurred: {str(e)}")
            return redirect('/blogs/coupons/create_coupon/')
        
    if request.method == "GET":
        coupon = get_object_or_404(Coupon, pk=pk)
        return render(request, "createCoupon.html", {'role':  "librarycommander", 'coupon': coupon, 'update': True})
    
