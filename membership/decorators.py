from django.shortcuts import redirect
from django.utils import timezone
from functools import wraps
from .models import Membership, Plan


def get_active_membership(librarian):
    try:
        membership = Membership.objects.get(librarian=librarian)
        if membership.expiry_date >= timezone.now().date():
            return membership
    except Membership.DoesNotExist:
        return None


def get_plan_hierarchy(plans):
    """Generate a hierarchy of plans dynamically."""
    hierarchy = {}
    for i, plan in enumerate(plans):
        hierarchy[plan] = plans[i:]  # Include the current plan and all plans below it
    return hierarchy


def membership_required(minimum_plan="Basic"):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not hasattr(request.user, "librarian_param"):
                return redirect("/librarian/signup")

            librarian = request.user.librarian_param
            membership = get_active_membership(librarian)

            if not membership:
                return redirect("/membership/plans")
            plans = [plan.name for plan in (Plan.objects.all().order_by("-id"))]
            plan_hierarchy = get_plan_hierarchy(plans)
            user_plan = membership.plan.name

            # Check if the user's plan is sufficient
            if minimum_plan in plan_hierarchy.get(user_plan, []):
                return view_func(request, *args, **kwargs)
            else:
                return redirect("/librarian/upgrade_plan")

        return _wrapped_view

    return decorator

def sublibrarian_membership_required(minimum_plan="Basic"):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not hasattr(request.user, "sublibrarian_param"):
                return redirect("/sublibrarian/login/")

            sublibrarian = request.user.sublibrarian_param
            librarian = sublibrarian.librarian

            membership = get_active_membership(librarian)
            if not membership:
                return redirect("/sublibrarian/access_denied/")  # Create this page

            plans = [plan.name for plan in Plan.objects.all().order_by("-id")]
            plan_hierarchy = get_plan_hierarchy(plans)
            user_plan = membership.plan.name

            if minimum_plan in plan_hierarchy.get(user_plan, []):
                return view_func(request, *args, **kwargs)
            else:
                return redirect("/sublibrarian/upgrade_needed/")  # Create this page

        return _wrapped_view
    return decorator